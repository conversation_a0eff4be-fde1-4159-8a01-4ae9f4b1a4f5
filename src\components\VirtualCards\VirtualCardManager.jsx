import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useWallet } from '../../context/WalletContext';
import BottomNavigation from '../Navigation/BottomNavigation';
import { 
  ArrowLeft, Plus, CreditCard, Eye, EyeOff, ToggleLeft, ToggleRight, 
  Copy, Calendar, Shield, Trash2, Settings, Smartphone, Zap, 
  Globe, Clock, Users, DollarSign
} from '../Icons/Icons';

const VirtualCardManager = () => {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedCard, setSelectedCard] = useState(null);
  const [showCardDetails, setShowCardDetails] = useState({});
  const [newCardData, setNewCardData] = useState({
    name: '',
    balance: '',
    color: 'gradient-virtual',
    type: 'instant' // instant, travel, emergency
  });
  const navigate = useNavigate();
  const { virtualCards, createVirtualCard, toggleVirtualCard, balance } = useWallet();

  const cardTypes = [
    {
      id: 'instant',
      name: 'Instant Card',
      description: 'For immediate payments & transfers',
      icon: Zap,
      color: 'from-blue-500 to-blue-600',
      features: ['No verification needed', 'Instant activation', 'Global acceptance']
    },
    {
      id: 'travel',
      name: 'Travel Card',
      description: 'Perfect for international trips',
      icon: Globe,
      color: 'from-green-500 to-green-600',
      features: ['Multi-currency support', 'No foreign fees', 'Emergency fund access']
    },
    {
      id: 'emergency',
      name: 'Emergency Card',
      description: 'For stranded situations',
      icon: Shield,
      color: 'from-red-500 to-red-600',
      features: ['24/7 availability', 'Emergency contacts', 'Instant fund requests']
    }
  ];

  const cardColors = [
    { name: 'Blue', value: 'from-blue-500 to-blue-600', gradient: 'from-blue-500 to-blue-600' },
    { name: 'Green', value: 'from-green-500 to-green-600', gradient: 'from-green-500 to-green-600' },
    { name: 'Purple', value: 'from-purple-500 to-purple-600', gradient: 'from-purple-500 to-purple-600' },
    { name: 'Orange', value: 'from-orange-500 to-orange-600', gradient: 'from-orange-500 to-orange-600' },
    { name: 'Red', value: 'from-red-500 to-red-600', gradient: 'from-red-500 to-red-600' },
    { name: 'Teal', value: 'from-teal-500 to-teal-600', gradient: 'from-teal-500 to-teal-600' }
  ];

  const handleCreateCard = async () => {
    if (!newCardData.name || !newCardData.balance) {
      alert('Please fill in all fields');
      return;
    }

    const cardBalance = parseFloat(newCardData.balance);
    if (cardBalance > balance) {
      alert('Insufficient wallet balance');
      return;
    }

    // Simulate instant card creation without verification
    const newCard = await createVirtualCard({
      ...newCardData,
      balance: cardBalance,
      isInstant: true,
      verificationRequired: false,
      activatedAt: new Date(),
      expiryDate: new Date(Date.now() + 4 * 365 * 24 * 60 * 60 * 1000) // 4 years
    });

    setNewCardData({ name: '', balance: '', color: 'from-blue-500 to-blue-600', type: 'instant' });
    setShowCreateModal(false);
  };

  const addToGooglePay = (card) => {
    // Simulate adding to Google Pay without verification
    alert(`Adding ${card.name} to Google Pay...\n\nCard details:\n${card.number}\nExp: ${card.expiry || '12/27'}\nCVV: ${card.cvv || '123'}\n\n✅ No bank verification required!`);
  };

  const addToSamsungPay = (card) => {
    // Simulate adding to Samsung Pay without verification
    alert(`Adding ${card.name} to Samsung Pay...\n\nCard details:\n${card.number}\nExp: ${card.expiry || '12/27'}\nCVV: ${card.cvv || '123'}\n\n✅ No bank verification required!`);
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    // Show toast notification
  };

  const toggleCardDetails = (cardId) => {
    setShowCardDetails(prev => ({
      ...prev,
      [cardId]: !prev[cardId]
    }));
  };

  const VirtualCard = ({ card, index }) => (
    <motion.div
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ delay: index * 0.1 }}
      className={`relative bg-gradient-to-br ${card.color} rounded-3xl p-6 text-white shadow-card mb-4`}
    >
      {/* Card Header */}
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold">{card.name}</h3>
          <p className="text-white/70 text-sm flex items-center">
            <Zap className="w-3 h-3 mr-1" />
            Instant • No Verification
          </p>
        </div>
        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={() => toggleVirtualCard(card.id)}
          className="p-2"
        >
          {card.isActive ? (
            <ToggleRight className="w-8 h-8 text-white" />
          ) : (
            <ToggleLeft className="w-8 h-8 text-white/60" />
          )}
        </motion.button>
      </div>

      {/* Card Number */}
      <div className="mb-4">
        <div className="flex items-center space-x-2 mb-2">
          <span className="text-xl font-mono tracking-wider">
            {showCardDetails[card.id] ? card.number : card.number.replace(/\d(?=\d{4})/g, '*')}
          </span>
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => toggleCardDetails(card.id)}
            className="p-1"
          >
            {showCardDetails[card.id] ? (
              <EyeOff className="w-4 h-4 text-white/80" />
            ) : (
              <Eye className="w-4 h-4 text-white/80" />
            )}
          </motion.button>
        </div>
        {showCardDetails[card.id] && (
          <div className="text-sm text-white/80">
            <p>Exp: {card.expiry || '12/27'} • CVV: {card.cvv || '123'}</p>
          </div>
        )}
      </div>

      {/* Card Details */}
      <div className="flex items-center justify-between mb-4">
        <div>
          <p className="text-white/70 text-xs">Available Balance</p>
          <p className="text-xl font-bold">${card.balance.toFixed(2)}</p>
        </div>
        <div className="text-right">
          <p className="text-white/70 text-xs">Status</p>
          <p className="font-semibold text-green-300">✓ Ready</p>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => addToGooglePay(card)}
            className="px-3 py-1 bg-white/20 rounded-full text-xs font-medium flex items-center space-x-1"
          >
            <Smartphone className="w-3 h-3" />
            <span>+ Google Pay</span>
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => addToSamsungPay(card)}
            className="px-3 py-1 bg-white/20 rounded-full text-xs font-medium flex items-center space-x-1"
          >
            <Smartphone className="w-3 h-3" />
            <span>+ Samsung Pay</span>
          </motion.button>
        </div>
        <div className="flex items-center space-x-2">
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => copyToClipboard(card.number)}
            className="p-2 bg-white/20 rounded-full"
          >
            <Copy className="w-4 h-4" />
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => setSelectedCard(card)}
            className="p-2 bg-white/20 rounded-full"
          >
            <Settings className="w-4 h-4" />
          </motion.button>
        </div>
      </div>

      {/* Inactive Overlay */}
      {!card.isActive && (
        <div className="absolute inset-0 bg-black/20 rounded-3xl flex items-center justify-center">
          <div className="text-center">
            <Shield className="w-12 h-12 text-white/80 mx-auto mb-2" />
            <p className="text-white/80 font-medium">Card Paused</p>
          </div>
        </div>
      )}
    </motion.div>
  );

  return (
    <div className="min-h-screen bg-greta-50 pb-20">
      {/* Header */}
      <motion.div
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="bg-white shadow-sm sticky top-0 z-10"
      >
        <div className="px-6 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => navigate('/dashboard')}
              className="p-2 hover:bg-greta-100 rounded-full transition-colors"
            >
              <ArrowLeft className="w-6 h-6 text-greta-700" />
            </motion.button>
            <div>
              <h1 className="text-xl font-bold text-greta-900">Virtual Cards</h1>
              <p className="text-sm text-greta-600">Instant • No verification needed</p>
            </div>
          </div>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setShowCreateModal(true)}
            className="bg-gradient-greta text-white p-3 rounded-full shadow-lg"
          >
            <Plus className="w-6 h-6" />
          </motion.button>
        </div>
      </motion.div>

      {/* Content */}
      <div className="px-6 py-6">
        {/* Traveler Benefits */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="bg-gradient-to-r from-blue-50 to-green-50 border border-blue-200 rounded-2xl p-6 mb-6"
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
              <Globe className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h3 className="font-semibold text-blue-800">Perfect for Travelers</h3>
              <p className="text-sm text-blue-600">No bank calls • 24/7 available • Instant activation</p>
            </div>
          </div>
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <Clock className="w-5 h-5 text-green-600 mx-auto mb-1" />
              <p className="text-xs text-greta-600">24/7 Access</p>
            </div>
            <div>
              <Shield className="w-5 h-5 text-green-600 mx-auto mb-1" />
              <p className="text-xs text-greta-600">No Verification</p>
            </div>
            <div>
              <Zap className="w-5 h-5 text-green-600 mx-auto mb-1" />
              <p className="text-xs text-greta-600">Instant Use</p>
            </div>
          </div>
        </motion.div>

        {/* Wallet Balance */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="bg-white rounded-2xl p-6 shadow-greta mb-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-greta-600 text-sm">Available to Load</p>
              <h2 className="text-2xl font-bold text-greta-900">${balance.toFixed(2)}</h2>
            </div>
            <div className="w-12 h-12 bg-gradient-greta rounded-full flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-white" />
            </div>
          </div>
        </motion.div>

        {/* Virtual Cards */}
        <div>
          <h3 className="text-lg font-semibold text-greta-900 mb-4">Your Cards</h3>
          {virtualCards.length > 0 ? (
            <div>
              {virtualCards.map((card, index) => (
                <VirtualCard key={card.id} card={card} index={index} />
              ))}
            </div>
          ) : (
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              className="text-center py-12"
            >
              <div className="w-16 h-16 bg-greta-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CreditCard className="w-8 h-8 text-greta-400" />
              </div>
              <h3 className="text-lg font-semibold text-greta-900 mb-2">No Cards Yet</h3>
              <p className="text-greta-600 mb-6">Create your first instant virtual card</p>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => setShowCreateModal(true)}
                className="bg-gradient-greta text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all"
              >
                Create Instant Card
              </motion.button>
            </motion.div>
          )}
        </div>
      </div>

      {/* Create Card Modal */}
      <AnimatePresence>
        {showCreateModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-50 flex items-end justify-center p-4"
            onClick={() => setShowCreateModal(false)}
          >
            <motion.div
              initial={{ y: 400 }}
              animate={{ y: 0 }}
              exit={{ y: 400 }}
              transition={{ type: "spring", damping: 25, stiffness: 200 }}
              onClick={(e) => e.stopPropagation()}
              className="bg-white rounded-t-3xl w-full max-w-md p-6 space-y-6 max-h-[90vh] overflow-y-auto"
            >
              <div className="text-center">
                <div className="w-12 h-12 bg-gradient-greta rounded-full flex items-center justify-center mx-auto mb-4">
                  <Zap className="w-6 h-6 text-white" />
                </div>
                <h2 className="text-xl font-bold text-greta-900 mb-2">Create Instant Card</h2>
                <p className="text-greta-600">No verification • Ready in seconds</p>
              </div>

              {/* Card Type Selection */}
              <div>
                <label className="block text-sm font-medium text-greta-700 mb-3">
                  Card Type
                </label>
                <div className="space-y-3">
                  {cardTypes.map((type) => (
                    <motion.button
                      key={type.id}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => setNewCardData(prev => ({ ...prev, type: type.id }))}
                      className={`w-full p-4 rounded-xl border-2 text-left transition-all ${
                        newCardData.type === type.id
                          ? 'border-primary-500 bg-primary-50'
                          : 'border-greta-300 bg-white'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <div className={`w-10 h-10 bg-gradient-to-br ${type.color} rounded-lg flex items-center justify-center`}>
                          <type.icon className="w-5 h-5 text-white" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-semibold text-greta-900">{type.name}</h4>
                          <p className="text-sm text-greta-600">{type.description}</p>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {type.features.map((feature, index) => (
                              <span key={index} className="text-xs bg-green-100 text-green-600 px-2 py-0.5 rounded-full">
                                {feature}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                    </motion.button>
                  ))}
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-greta-700 mb-2">
                    Card Name
                  </label>
                  <input
                    type="text"
                    value={newCardData.name}
                    onChange={(e) => setNewCardData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="e.g., Travel Card, Emergency Fund"
                    className="w-full px-4 py-3 border border-greta-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent outline-none"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-greta-700 mb-2">
                    Load Amount
                  </label>
                  <input
                    type="number"
                    value={newCardData.balance}
                    onChange={(e) => setNewCardData(prev => ({ ...prev, balance: e.target.value }))}
                    placeholder="0.00"
                    max={balance}
                    className="w-full px-4 py-3 border border-greta-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent outline-none"
                  />
                  <p className="text-xs text-greta-500 mt-1">Available: ${balance.toFixed(2)}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-greta-700 mb-2">
                    Card Color
                  </label>
                  <div className="grid grid-cols-3 gap-2">
                    {cardColors.map((color) => (
                      <motion.button
                        key={color.value}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setNewCardData(prev => ({ ...prev, color: color.value }))}
                        className={`h-12 bg-gradient-to-br ${color.gradient} rounded-lg border-2 transition-all ${
                          newCardData.color === color.value ? 'border-white shadow-lg' : 'border-transparent'
                        }`}
                      />
                    ))}
                  </div>
                </div>
              </div>

              {/* Instant Activation Notice */}
              <div className="bg-green-50 border border-green-200 rounded-xl p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <Zap className="w-4 h-4 text-green-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-green-800">Instant Activation</h4>
                    <p className="text-sm text-green-600">
                      Card will be ready immediately with no verification required
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex space-x-3">
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => setShowCreateModal(false)}
                  className="flex-1 bg-greta-200 text-greta-700 py-3 rounded-xl font-semibold"
                >
                  Cancel
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={handleCreateCard}
                  className="flex-1 bg-gradient-greta text-white py-3 rounded-xl font-semibold shadow-lg"
                >
                  Create Instantly
                </motion.button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      <BottomNavigation />
    </div>
  );
};

export default VirtualCardManager;