import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useWallet } from '../../context/WalletContext';
import BottomNavigation from '../Navigation/BottomNavigation';
import { 
  ArrowLeft, 
  Search, 
  Filter, 
  ArrowUpRight, 
  ArrowDownLeft, 
  Send, 
  Wallet, 
  Calendar, 
  DollarSign 
} from '../Icons/Icons';
import { format, isToday, isYesterday, startOfWeek, endOfWeek } from 'date-fns';

const TransactionHistory = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('all'); // all, payment, transfer, topup
  const [selectedPeriod, setSelectedPeriod] = useState('all'); // all, today, week, month
  const navigate = useNavigate();
  const { transactions } = useWallet();

  const filterOptions = [
    { value: 'all', label: 'All Transactions' },
    { value: 'payment', label: 'Payments' },
    { value: 'transfer', label: 'Transfers' },
    { value: 'topup', label: 'Top-ups' }
  ];

  const periodOptions = [
    { value: 'all', label: 'All Time' },
    { value: 'today', label: 'Today' },
    { value: 'week', label: 'This Week' },
    { value: 'month', label: 'This Month' }
  ];

  const getTransactionIcon = (type) => {
    const iconProps = "w-5 h-5";
    switch (type) {
      case 'payment':
        return <ArrowUpRight className={`${iconProps} text-red-500`} />;
      case 'transfer':
        return <Send className={`${iconProps} text-blue-500`} />;
      case 'topup':
        return <ArrowDownLeft className={`${iconProps} text-green-500`} />;
      default:
        return <Wallet className={`${iconProps} text-greta-500`} />;
    }
  };

  const filterTransactions = () => {
    let filtered = transactions;

    // Filter by type
    if (filterType !== 'all') {
      filtered = filtered.filter(t => t.type === filterType);
    }

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(t => 
        t.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        t.merchant?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        t.recipient?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filter by period
    if (selectedPeriod !== 'all') {
      const now = new Date();
      filtered = filtered.filter(t => {
        switch (selectedPeriod) {
          case 'today':
            return isToday(t.date);
          case 'week':
            return t.date >= startOfWeek(now) && t.date <= endOfWeek(now);
          case 'month':
            return t.date.getMonth() === now.getMonth() && t.date.getFullYear() === now.getFullYear();
          default:
            return true;
        }
      });
    }

    return filtered;
  };

  const groupTransactionsByDate = (transactions) => {
    const groups = {};
    
    transactions.forEach(transaction => {
      let dateKey;
      if (isToday(transaction.date)) {
        dateKey = 'Today';
      } else if (isYesterday(transaction.date)) {
        dateKey = 'Yesterday';
      } else {
        dateKey = format(transaction.date, 'MMMM d, yyyy');
      }

      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(transaction);
    });

    return groups;
  };

  const filteredTransactions = filterTransactions();
  const groupedTransactions = groupTransactionsByDate(filteredTransactions);
  const totalAmount = filteredTransactions.reduce((sum, t) => sum + Math.abs(t.amount), 0);

  return (
    <div className="min-h-screen bg-greta-50 pb-20">
      {/* Header */}
      <motion.div
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="bg-white shadow-sm sticky top-0 z-10"
      >
        <div className="px-6 py-4">
          <div className="flex items-center space-x-4 mb-4">
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => navigate('/dashboard')}
              className="p-2 hover:bg-greta-100 rounded-full transition-colors"
            >
              <ArrowLeft className="w-6 h-6 text-greta-700" />
            </motion.button>
            <div className="flex-1">
              <h1 className="text-xl font-bold text-greta-900">Transaction History</h1>
              <p className="text-sm text-greta-600">{filteredTransactions.length} transactions</p>
            </div>
          </div>

          {/* Search Bar */}
          <div className="relative mb-4">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search transactions..."
              className="w-full pl-10 pr-4 py-3 bg-greta-50 border border-greta-200 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent outline-none"
            />
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-greta-400" />
          </div>

          {/* Filters */}
          <div className="flex space-x-2 overflow-x-auto pb-2">
            {filterOptions.map((option) => (
              <motion.button
                key={option.value}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => setFilterType(option.value)}
                className={`px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-all ${
                  filterType === option.value
                    ? 'bg-primary-600 text-white'
                    : 'bg-greta-100 text-greta-600 hover:bg-greta-200'
                }`}
              >
                {option.label}
              </motion.button>
            ))}
          </div>
        </div>
      </motion.div>

      <div className="px-6 py-6">
        {/* Summary */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="bg-white rounded-2xl p-6 shadow-greta mb-6"
        >
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <DollarSign className="w-6 h-6 text-blue-600" />
              </div>
              <p className="text-sm text-greta-600">Total Volume</p>
              <p className="text-xl font-bold text-greta-900">${totalAmount.toFixed(2)}</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <Calendar className="w-6 h-6 text-green-600" />
              </div>
              <p className="text-sm text-greta-600">Transactions</p>
              <p className="text-xl font-bold text-greta-900">{filteredTransactions.length}</p>
            </div>
          </div>
        </motion.div>

        {/* Period Filter */}
        <div className="flex space-x-2 mb-6 overflow-x-auto">
          {periodOptions.map((option) => (
            <motion.button
              key={option.value}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => setSelectedPeriod(option.value)}
              className={`px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-all ${
                selectedPeriod === option.value
                  ? 'bg-gradient-greta text-white'
                  : 'bg-white text-greta-600 hover:bg-greta-50 border border-greta-200'
              }`}
            >
              {option.label}
            </motion.button>
          ))}
        </div>

        {/* Transactions */}
        {Object.keys(groupedTransactions).length > 0 ? (
          <div className="space-y-6">
            {Object.entries(groupedTransactions).map(([date, transactions], groupIndex) => (
              <motion.div
                key={date}
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: groupIndex * 0.1 }}
              >
                <h3 className="text-sm font-semibold text-greta-700 mb-3 px-2">{date}</h3>
                <div className="bg-white rounded-2xl shadow-greta overflow-hidden">
                  {transactions.map((transaction, index) => (
                    <motion.div
                      key={transaction.id}
                      initial={{ x: -20, opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      transition={{ delay: (groupIndex * 0.1) + (index * 0.05) }}
                      className="p-4 border-b border-greta-100 last:border-b-0 flex items-center justify-between hover:bg-greta-50 transition-colors"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-greta-100 rounded-full flex items-center justify-center">
                          {getTransactionIcon(transaction.type)}
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium text-greta-900">{transaction.description}</h4>
                          <div className="flex items-center space-x-2 text-sm text-greta-500">
                            <span>{format(transaction.date, 'h:mm a')}</span>
                            <span>•</span>
                            <span>{transaction.method}</span>
                            {transaction.status && (
                              <>
                                <span>•</span>
                                <span className="capitalize">{transaction.status}</span>
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`font-semibold ${
                          transaction.amount > 0 ? 'text-green-600' : 'text-greta-900'
                        }`}>
                          {transaction.amount > 0 ? '+' : ''}${Math.abs(transaction.amount).toFixed(2)}
                        </p>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        ) : (
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            className="text-center py-12"
          >
            <div className="w-16 h-16 bg-greta-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Search className="w-8 h-8 text-greta-400" />
            </div>
            <h3 className="text-lg font-semibold text-greta-900 mb-2">No Transactions Found</h3>
            <p className="text-greta-600 mb-6">
              {searchQuery || filterType !== 'all' || selectedPeriod !== 'all'
                ? 'Try adjusting your search or filters'
                : 'Start making transactions to see your history here'}
            </p>
            {(searchQuery || filterType !== 'all' || selectedPeriod !== 'all') && (
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => {
                  setSearchQuery('');
                  setFilterType('all');
                  setSelectedPeriod('all');
                }}
                className="bg-gradient-greta text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all"
              >
                Clear Filters
              </motion.button>
            )}
          </motion.div>
        )}
      </div>

      <BottomNavigation />
    </div>
  );
};

export default TransactionHistory;