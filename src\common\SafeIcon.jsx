import React from 'react';
import * as FiIcons from 'react-icons/fi';
import { ArrowLeft } from '../components/Icons/Icons';

const SafeIcon = ({ icon, name, ...props }) => {
  let IconComponent;
  
  try {
    IconComponent = icon || (name && FiIcons[`Fi${name}`]);
  } catch (e) {
    IconComponent = null;
  }
  
  return IconComponent ? React.createElement(IconComponent, props) : <ArrowLeft {...props} />;
};

export default SafeIcon;