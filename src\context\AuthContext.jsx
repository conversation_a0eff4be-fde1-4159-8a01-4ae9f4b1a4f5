import React, { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate auth check
    const savedUser = localStorage.getItem('greta-wallet-user');
    if (savedUser) {
      setUser(JSON.parse(savedUser));
      setIsAuthenticated(true);
    }
    setLoading(false);
  }, []);

  const login = async (email, password) => {
    // Simulate login
    const mockUser = {
      id: '1',
      email,
      name: '<PERSON>',
      phone: '+****************',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
    };

    setUser(mockUser);
    setIsAuthenticated(true);
    localStorage.setItem('greta-wallet-user', JSON.stringify(mockUser));
    return mockUser;
  };

  const loginAsGuest = () => {
    const guestUser = {
      id: 'guest',
      email: '<EMAIL>',
      name: 'Guest User',
      phone: '+****************',
      avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face',
      isGuest: true
    };

    setUser(guestUser);
    setIsAuthenticated(true);
    localStorage.setItem('greta-wallet-user', JSON.stringify(guestUser));
    return guestUser;
  };

  const logout = () => {
    setUser(null);
    setIsAuthenticated(false);
    localStorage.removeItem('greta-wallet-user');
  };

  const value = {
    user,
    isAuthenticated,
    loading,
    login,
    loginAsGuest,
    logout
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};