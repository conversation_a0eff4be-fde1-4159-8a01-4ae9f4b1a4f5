import React from 'react';
import { motion } from 'framer-motion';
import { useNavigate, useLocation } from 'react-router-dom';
import { Home, Send, CreditCard, Shield, User } from '../Icons/Icons';

const BottomNavigation = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const navItems = [
    { id: 'home', icon: Home, label: 'Home', route: '/dashboard' },
    { id: 'send', icon: Send, label: 'Send', route: '/send' },
    { id: 'cards', icon: CreditCard, label: 'Cards', route: '/cards' },
    { id: 'emergency', icon: Shield, label: 'Emergency', route: '/emergency' },
    { id: 'profile', icon: User, label: 'Profile', route: '/settings' }
  ];

  const isActive = (route) => location.pathname === route;

  return (
    <motion.div
      initial={{ y: 100 }}
      animate={{ y: 0 }}
      className="fixed bottom-0 left-0 right-0 bg-white border-t border-greta-200 px-6 py-3 safe-bottom"
    >
      <div className="flex items-center justify-around">
        {navItems.map((item) => {
          const IconComponent = item.icon;
          const active = isActive(item.route);

          return (
            <motion.button
              key={item.id}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => navigate(item.route)}
              className="flex flex-col items-center space-y-1 py-2 px-3 relative"
            >
              {active && (
                <motion.div
                  layoutId="activeTab"
                  className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary-600 rounded-full"
                />
              )}
              <div className={`p-2 rounded-xl transition-all duration-200 ${
                active ? 'bg-primary-100 text-primary-600' : 'text-greta-500 hover:text-greta-700'
              }`}>
                <IconComponent className="w-5 h-5" />
              </div>
              <span className={`text-xs font-medium transition-all duration-200 ${
                active ? 'text-primary-600' : 'text-greta-500'
              }`}>
                {item.label}
              </span>
            </motion.button>
          );
        })}
      </div>
    </motion.div>
  );
};

export default BottomNavigation;