import React, { createContext, useContext, useState, useEffect } from 'react';

const WalletContext = createContext();

export const useWallet = () => {
  const context = useContext(WalletContext);
  if (!context) {
    throw new Error('useWallet must be used within a WalletProvider');
  }
  return context;
};

export const WalletProvider = ({ children }) => {
  const [balance, setBalance] = useState(2500.75); // Higher balance for travelers
  const [cards, setCards] = useState([
    {
      id: '1',
      type: 'credit',
      brand: 'Visa',
      last4: '4242',
      name: 'Primary Card',
      isDefault: true
    },
    {
      id: '2',
      type: 'debit',
      brand: 'Mastercard',
      last4: '8888',
      name: 'Savings Account',
      isDefault: false
    }
  ]);

  const [virtualCards, setVirtualCards] = useState([
    {
      id: 'vc1',
      name: 'Travel Card',
      number: '5555 **** **** 1234',
      balance: 1000.00,
      isActive: true,
      created: new Date('2024-01-15'),
      color: 'from-green-500 to-green-600',
      type: 'travel',
      expiry: '12/27',
      cvv: '123',
      isInstant: true,
      verificationRequired: false
    },
    {
      id: 'vc2',
      name: 'Emergency Fund',
      number: '4444 **** **** 5678',
      balance: 500.00,
      isActive: true,
      created: new Date('2024-01-10'),
      color: 'from-red-500 to-red-600',
      type: 'emergency',
      expiry: '12/27',
      cvv: '456',
      isInstant: true,
      verificationRequired: false
    }
  ]);

  const [transactions, setTransactions] = useState([
    {
      id: '1',
      type: 'payment',
      amount: -45.50,
      description: 'Airport Coffee',
      date: new Date('2024-01-20T10:30:00'),
      status: 'completed',
      merchant: 'Terminal Cafe',
      method: 'NFC',
      location: 'JFK Airport'
    },
    {
      id: '2',
      type: 'transfer',
      amount: -200.00,
      description: 'Emergency funds to friend',
      date: new Date('2024-01-19T15:45:00'),
      status: 'completed',
      recipient: 'Sarah Johnson',
      method: 'Telegram Bot'
    },
    {
      id: '3',
      type: 'topup',
      amount: 1000.00,
      description: 'Travel fund loading',
      date: new Date('2024-01-18T09:15:00'),
      status: 'completed',
      method: 'Instant Load'
    },
    {
      id: '4',
      type: 'payment',
      amount: -89.99,
      description: 'Hotel booking',
      date: new Date('2024-01-17T14:20:00'),
      status: 'completed',
      merchant: 'TravelBooking.com',
      method: 'Virtual Card'
    },
    {
      id: '5',
      type: 'emergency',
      amount: 300.00,
      description: 'Emergency fund received',
      date: new Date('2024-01-16T20:30:00'),
      status: 'completed',
      sender: 'Emergency Contact',
      method: 'Instant Transfer'
    }
  ]);

  const addTransaction = (transaction) => {
    const newTransaction = {
      ...transaction,
      id: Date.now().toString(),
      date: new Date(),
      status: 'completed'
    };

    setTransactions(prev => [newTransaction, ...prev]);

    if (transaction.amount < 0) {
      setBalance(prev => prev + transaction.amount);
    } else {
      setBalance(prev => prev + transaction.amount);
    }
  };

  const createVirtualCard = (cardData) => {
    const newCard = {
      id: `vc${Date.now()}`,
      ...cardData,
      number: `${Math.floor(Math.random() * 9000) + 1000} **** **** ${Math.floor(Math.random() * 9000) + 1000}`,
      created: new Date(),
      isActive: true,
      expiry: '12/27',
      cvv: Math.floor(Math.random() * 900) + 100,
      isInstant: true,
      verificationRequired: false
    };

    setVirtualCards(prev => [newCard, ...prev]);
    
    // Deduct balance for card loading
    if (cardData.balance) {
      setBalance(prev => prev - cardData.balance);
    }

    return newCard;
  };

  const toggleVirtualCard = (cardId) => {
    setVirtualCards(prev =>
      prev.map(card =>
        card.id === cardId ? { ...card, isActive: !card.isActive } : card
      )
    );
  };

  const topUpWallet = (amount, method = 'Instant Load') => {
    setBalance(prev => prev + amount);
    addTransaction({
      type: 'topup',
      amount: amount,
      description: 'Wallet Top-up',
      method: method
    });
  };

  const requestEmergencyFunds = (contact, amount, message) => {
    // Simulate emergency fund request
    const transaction = {
      type: 'emergency_request',
      amount: amount,
      description: `Emergency request: ${message}`,
      recipient: contact.name,
      method: 'Emergency Request',
      status: 'pending'
    };

    addTransaction(transaction);

    // Simulate auto-approval for demo
    setTimeout(() => {
      addTransaction({
        type: 'emergency',
        amount: amount,
        description: `Emergency funds from ${contact.name}`,
        sender: contact.name,
        method: 'Emergency Transfer'
      });
    }, 5000);
  };

  const loadCardInstantly = (cardId, amount, source = 'wallet') => {
    setVirtualCards(prev =>
      prev.map(card =>
        card.id === cardId 
          ? { ...card, balance: card.balance + amount }
          : card
      )
    );

    if (source === 'wallet') {
      setBalance(prev => prev - amount);
    }

    addTransaction({
      type: 'card_load',
      amount: -amount,
      description: `Loaded ${cardId}`,
      method: 'Instant Load'
    });
  };

  const value = {
    balance,
    cards,
    virtualCards,
    transactions,
    addTransaction,
    createVirtualCard,
    toggleVirtualCard,
    topUpWallet,
    requestEmergencyFunds,
    loadCardInstantly,
    setBalance
  };

  return (
    <WalletContext.Provider value={value}>
      {children}
    </WalletContext.Provider>
  );
};