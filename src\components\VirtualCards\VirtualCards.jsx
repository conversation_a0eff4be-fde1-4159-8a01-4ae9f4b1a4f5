import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useWallet } from '../../context/WalletContext';
import BottomNavigation from '../Navigation/BottomNavigation';
import { 
  ArrowLeft, 
  Plus, 
  CreditCard, 
  Eye, 
  EyeOff, 
  ToggleLeft, 
  ToggleRight, 
  Copy, 
  Calendar, 
  Shield, 
  Trash2, 
  Settings 
} from '../Icons/Icons';

const VirtualCards = () => {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedCard, setSelectedCard] = useState(null);
  const [showCardDetails, setShowCardDetails] = useState({});
  const [newCardData, setNewCardData] = useState({
    name: '',
    balance: '',
    color: 'gradient-virtual'
  });
  const navigate = useNavigate();
  const { virtualCards, createVirtualCard, toggleVirtualCard, balance } = useWallet();

  const cardColors = [
    { name: 'Green', value: 'gradient-virtual', gradient: 'from-green-500 to-green-600' },
    { name: 'Blue', value: 'gradient-card', gradient: 'from-blue-500 to-blue-600' },
    { name: 'Purple', value: 'from-purple-500 to-purple-600', gradient: 'from-purple-500 to-purple-600' },
    { name: 'Orange', value: 'from-orange-500 to-orange-600', gradient: 'from-orange-500 to-orange-600' }
  ];

  const handleCreateCard = () => {
    if (!newCardData.name || !newCardData.balance) {
      alert('Please fill in all fields');
      return;
    }

    const cardBalance = parseFloat(newCardData.balance);
    if (cardBalance > balance) {
      alert('Insufficient wallet balance');
      return;
    }

    createVirtualCard({
      name: newCardData.name,
      balance: cardBalance,
      color: newCardData.color
    });

    setNewCardData({ name: '', balance: '', color: 'gradient-virtual' });
    setShowCreateModal(false);
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    // In a real app, show a toast notification
  };

  const toggleCardDetails = (cardId) => {
    setShowCardDetails(prev => ({
      ...prev,
      [cardId]: !prev[cardId]
    }));
  };

  const VirtualCard = ({ card, index }) => (
    <motion.div
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ delay: index * 0.1 }}
      className={`relative bg-gradient-to-br ${card.color} rounded-3xl p-6 text-white shadow-card mb-4`}
    >
      {/* Card Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold">{card.name}</h3>
          <p className="text-white/70 text-sm">Virtual Card</p>
        </div>
        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={() => toggleVirtualCard(card.id)}
          className="p-2"
        >
          {card.isActive ? (
            <ToggleRight className="w-8 h-8 text-white" />
          ) : (
            <ToggleLeft className="w-8 h-8 text-white/60" />
          )}
        </motion.button>
      </div>

      {/* Card Number */}
      <div className="mb-6">
        <div className="flex items-center space-x-2 mb-2">
          <span className="text-2xl font-mono tracking-wider">
            {showCardDetails[card.id] 
              ? card.number 
              : card.number.replace(/\d(?=\d{4})/g, '*')
            }
          </span>
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => toggleCardDetails(card.id)}
            className="p-1"
          >
            {showCardDetails[card.id] ? (
              <EyeOff className="w-4 h-4 text-white/80" />
            ) : (
              <Eye className="w-4 h-4 text-white/80" />
            )}
          </motion.button>
        </div>
      </div>

      {/* Card Details */}
      <div className="flex items-center justify-between mb-4">
        <div>
          <p className="text-white/70 text-xs">Balance</p>
          <p className="text-xl font-bold">${card.balance.toFixed(2)}</p>
        </div>
        <div className="text-right">
          <p className="text-white/70 text-xs">Expires</p>
          <p className="font-semibold">12/27</p>
        </div>
      </div>

      {/* Card Status */}
      <div className="flex items-center justify-between">
        <div className={`px-3 py-1 rounded-full text-xs font-medium ${
          card.isActive 
            ? 'bg-white/20 text-white' 
            : 'bg-white/10 text-white/60'
        }`}>
          {card.isActive ? 'Active' : 'Inactive'}
        </div>
        <div className="flex items-center space-x-2">
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => copyToClipboard(card.number)}
            className="p-2 bg-white/20 rounded-full"
          >
            <Copy className="w-4 h-4" />
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => setSelectedCard(card)}
            className="p-2 bg-white/20 rounded-full"
          >
            <Settings className="w-4 h-4" />
          </motion.button>
        </div>
      </div>

      {/* Inactive Overlay */}
      {!card.isActive && (
        <div className="absolute inset-0 bg-black/20 rounded-3xl flex items-center justify-center">
          <div className="text-center">
            <Shield className="w-12 h-12 text-white/80 mx-auto mb-2" />
            <p className="text-white/80 font-medium">Card Disabled</p>
          </div>
        </div>
      )}
    </motion.div>
  );

  return (
    <div className="min-h-screen bg-greta-50 pb-20">
      {/* Header */}
      <motion.div
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="bg-white shadow-sm sticky top-0 z-10"
      >
        <div className="px-6 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => navigate('/dashboard')}
              className="p-2 hover:bg-greta-100 rounded-full transition-colors"
            >
              <ArrowLeft className="w-6 h-6 text-greta-700" />
            </motion.button>
            <div>
              <h1 className="text-xl font-bold text-greta-900">Virtual Cards</h1>
              <p className="text-sm text-greta-600">{virtualCards.length} cards</p>
            </div>
          </div>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setShowCreateModal(true)}
            className="bg-gradient-greta text-white p-3 rounded-full shadow-lg"
          >
            <Plus className="w-6 h-6" />
          </motion.button>
        </div>
      </motion.div>

      {/* Content */}
      <div className="px-6 py-6">
        {/* Wallet Balance */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="bg-white rounded-2xl p-6 shadow-greta mb-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-greta-600 text-sm">Available Balance</p>
              <h2 className="text-2xl font-bold text-greta-900">${balance.toFixed(2)}</h2>
            </div>
            <div className="w-12 h-12 bg-gradient-greta rounded-full flex items-center justify-center">
              <CreditCard className="w-6 h-6 text-white" />
            </div>
          </div>
        </motion.div>

        {/* Virtual Cards */}
        <div>
          <h3 className="text-lg font-semibold text-greta-900 mb-4">Your Virtual Cards</h3>
          {virtualCards.length > 0 ? (
            <div>
              {virtualCards.map((card, index) => (
                <VirtualCard key={card.id} card={card} index={index} />
              ))}
            </div>
          ) : (
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              className="text-center py-12"
            >
              <div className="w-16 h-16 bg-greta-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CreditCard className="w-8 h-8 text-greta-400" />
              </div>
              <h3 className="text-lg font-semibold text-greta-900 mb-2">No Virtual Cards</h3>
              <p className="text-greta-600 mb-6">Create your first virtual card for secure online shopping</p>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => setShowCreateModal(true)}
                className="bg-gradient-greta text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all"
              >
                Create Virtual Card
              </motion.button>
            </motion.div>
          )}
        </div>
      </div>

      {/* Create Card Modal */}
      <AnimatePresence>
        {showCreateModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-50 flex items-end justify-center p-4"
            onClick={() => setShowCreateModal(false)}
          >
            <motion.div
              initial={{ y: 400 }}
              animate={{ y: 0 }}
              exit={{ y: 400 }}
              transition={{ type: "spring", damping: 25, stiffness: 200 }}
              onClick={(e) => e.stopPropagation()}
              className="bg-white rounded-t-3xl w-full max-w-md p-6 space-y-6"
            >
              <div className="text-center">
                <div className="w-12 h-12 bg-gradient-greta rounded-full flex items-center justify-center mx-auto mb-4">
                  <Plus className="w-6 h-6 text-white" />
                </div>
                <h2 className="text-xl font-bold text-greta-900 mb-2">Create Virtual Card</h2>
                <p className="text-greta-600">Set up a new virtual card for secure transactions</p>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-greta-700 mb-2">
                    Card Name
                  </label>
                  <input
                    type="text"
                    value={newCardData.name}
                    onChange={(e) => setNewCardData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="e.g., Online Shopping, Subscriptions"
                    className="w-full px-4 py-3 border border-greta-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent outline-none"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-greta-700 mb-2">
                    Initial Balance
                  </label>
                  <input
                    type="number"
                    value={newCardData.balance}
                    onChange={(e) => setNewCardData(prev => ({ ...prev, balance: e.target.value }))}
                    placeholder="0.00"
                    max={balance}
                    className="w-full px-4 py-3 border border-greta-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent outline-none"
                  />
                  <p className="text-xs text-greta-500 mt-1">Available: ${balance.toFixed(2)}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-greta-700 mb-2">
                    Card Color
                  </label>
                  <div className="grid grid-cols-4 gap-2">
                    {cardColors.map((color) => (
                      <motion.button
                        key={color.value}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setNewCardData(prev => ({ ...prev, color: color.value }))}
                        className={`h-12 bg-gradient-to-br ${color.gradient} rounded-lg border-2 transition-all ${
                          newCardData.color === color.value
                            ? 'border-white shadow-lg'
                            : 'border-transparent'
                        }`}
                      />
                    ))}
                  </div>
                </div>
              </div>

              <div className="flex space-x-3">
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => setShowCreateModal(false)}
                  className="flex-1 bg-greta-200 text-greta-700 py-3 rounded-xl font-semibold"
                >
                  Cancel
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={handleCreateCard}
                  className="flex-1 bg-gradient-greta text-white py-3 rounded-xl font-semibold shadow-lg"
                >
                  Create Card
                </motion.button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      <BottomNavigation />
    </div>
  );
};

export default VirtualCards;