@import url('https://fonts.googleapis.com/css2?family=Gilroy:wght@300;400;500;600;700;800&display=swap');

:root {
  /* Color palette */
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-200: #bae6fd;
  --primary-300: #7dd3fc;
  --primary-400: #38bdf8;
  --primary-500: #0ea5e9;
  --primary-600: #0284c7;
  --primary-700: #0369a1;
  --primary-800: #075985;
  --primary-900: #0c4a6e;
  
  --greta-50: #fafafa;
  --greta-100: #f4f4f5;
  --greta-200: #e4e4e7;
  --greta-300: #d4d4d8;
  --greta-400: #a1a1aa;
  --greta-500: #71717a;
  --greta-600: #52525b;
  --greta-700: #3f3f46;
  --greta-800: #27272a;
  --greta-900: #18181b;

  /* Gradients */
  --gradient-greta: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-card: linear-gradient(135deg, #1e3a8a 0%, #3730a3 50%, #581c87 100%);
  --gradient-virtual: linear-gradient(135deg, #059669 0%, #0d9488 50%, #0f766e 100%);

  /* Shadows */
  --shadow-greta: 0 4px 20px rgba(0, 0, 0, 0.08);
  --shadow-card: 0 8px 32px rgba(0, 0, 0, 0.12);
  --shadow-float: 0 12px 40px rgba(0, 0, 0, 0.15);

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;

  /* Border radius */
  --radius-sm: 0.125rem;
  --radius: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Gilroy', 'Inter', system-ui, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--greta-50);
  color: var(--greta-900);
  line-height: 1.5;
}

#root {
  width: 100%;
  min-height: 100vh;
}

/* Utility classes */
.font-gilroy { font-family: 'Gilroy', 'Inter', system-ui, sans-serif; }

/* Display */
.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }

/* Flex utilities */
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-end { justify-content: flex-end; }
.flex-1 { flex: 1 1 0%; }

/* Grid utilities */
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }

/* Spacing */
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }
.px-2 { padding-left: var(--space-2); padding-right: var(--space-2); }
.px-3 { padding-left: var(--space-3); padding-right: var(--space-3); }
.px-4 { padding-left: var(--space-4); padding-right: var(--space-4); }
.px-6 { padding-left: var(--space-6); padding-right: var(--space-6); }
.px-8 { padding-left: var(--space-8); padding-right: var(--space-8); }
.py-1 { padding-top: var(--space-1); padding-bottom: var(--space-1); }
.py-2 { padding-top: var(--space-2); padding-bottom: var(--space-2); }
.py-3 { padding-top: var(--space-3); padding-bottom: var(--space-3); }
.py-4 { padding-top: var(--space-4); padding-bottom: var(--space-4); }
.py-6 { padding-top: var(--space-6); padding-bottom: var(--space-6); }
.py-8 { padding-top: var(--space-8); padding-bottom: var(--space-8); }
.py-12 { padding-top: var(--space-12); padding-bottom: var(--space-12); }

.m-2 { margin: var(--space-2); }
.m-4 { margin: var(--space-4); }
.mx-auto { margin-left: auto; margin-right: auto; }
.mb-2 { margin-bottom: var(--space-2); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }
.mb-8 { margin-bottom: var(--space-8); }
.mt-2 { margin-top: var(--space-2); }
.mt-4 { margin-top: var(--space-4); }
.mt-6 { margin-top: var(--space-6); }
.mt-8 { margin-top: var(--space-8); }

/* Width & Height */
.w-full { width: 100%; }
.w-1 { width: var(--space-1); }
.w-2 { width: var(--space-2); }
.w-3 { width: var(--space-3); }
.w-4 { width: var(--space-4); }
.w-5 { width: var(--space-5); }
.w-8 { width: var(--space-8); }
.w-10 { width: 2.5rem; }
.w-12 { width: var(--space-12); }
.w-16 { width: var(--space-16); }
.w-24 { width: var(--space-24); }
.w-32 { width: 8rem; }
.h-1 { height: var(--space-1); }
.h-2 { height: var(--space-2); }
.h-3 { height: var(--space-3); }
.h-4 { height: var(--space-4); }
.h-5 { height: var(--space-5); }
.h-8 { height: var(--space-8); }
.h-10 { height: 2.5rem; }
.h-12 { height: var(--space-12); }
.h-16 { height: var(--space-16); }
.h-24 { height: var(--space-24); }
.h-32 { height: 8rem; }
.min-h-screen { min-height: 100vh; }

/* Colors */
.bg-white { background-color: white; }
.bg-greta-50 { background-color: var(--greta-50); }
.bg-greta-100 { background-color: var(--greta-100); }
.bg-greta-200 { background-color: var(--greta-200); }
.bg-primary-50 { background-color: var(--primary-50); }
.bg-primary-100 { background-color: var(--primary-100); }
.bg-primary-600 { background-color: var(--primary-600); }
.bg-blue-50 { background-color: #eff6ff; }
.bg-blue-100 { background-color: #dbeafe; }
.bg-blue-500 { background-color: #3b82f6; }
.bg-green-100 { background-color: #dcfce7; }
.bg-green-500 { background-color: #22c55e; }
.bg-green-600 { background-color: #16a34a; }
.bg-purple-100 { background-color: #f3e8ff; }
.bg-purple-500 { background-color: #a855f7; }
.bg-orange-100 { background-color: #fed7aa; }
.bg-orange-500 { background-color: #f97316; }
.bg-red-50 { background-color: #fef2f2; }
.bg-red-100 { background-color: #fee2e2; }
.bg-red-600 { background-color: #dc2626; }
.bg-yellow-100 { background-color: #fef3c7; }
.bg-indigo-100 { background-color: #e0e7ff; }
.bg-gray-100 { background-color: #f3f4f6; }

.bg-gradient-greta { background: var(--gradient-greta); }
.bg-gradient-card { background: var(--gradient-card); }
.bg-gradient-virtual { background: var(--gradient-virtual); }

.text-white { color: white; }
.text-greta-500 { color: var(--greta-500); }
.text-greta-600 { color: var(--greta-600); }
.text-greta-700 { color: var(--greta-700); }
.text-greta-900 { color: var(--greta-900); }
.text-primary-600 { color: var(--primary-600); }
.text-primary-700 { color: var(--primary-700); }
.text-blue-600 { color: #2563eb; }
.text-blue-800 { color: #1e40af; }
.text-green-600 { color: var(--green-600); }
.text-red-500 { color: #ef4444; }
.text-red-600 { color: #dc2626; }
.text-orange-600 { color: #ea580c; }
.text-yellow-600 { color: #ca8a04; }
.text-purple-600 { color: #9333ea; }
.text-indigo-600 { color: #4f46e5; }

/* Typography */
.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }
.text-5xl { font-size: 3rem; }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* Border */
.border { border-width: 1px; border-style: solid; }
.border-2 { border-width: 2px; border-style: solid; }
.border-3 { border-width: 3px; border-style: solid; }
.border-b { border-bottom-width: 1px; border-bottom-style: solid; }
.border-t { border-top-width: 1px; border-top-style: solid; }
.border-greta-100 { border-color: var(--greta-100); }
.border-greta-200 { border-color: var(--greta-200); }
.border-greta-300 { border-color: var(--greta-300); }
.border-primary-200 { border-color: var(--primary-200); }
.border-primary-500 { border-color: var(--primary-500); }
.border-blue-200 { border-color: #bfdbfe; }
.border-red-200 { border-color: #fecaca; }
.border-white { border-color: white; }
.border-transparent { border-color: transparent; }

/* Border radius */
.rounded { border-radius: var(--radius); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-3xl { border-radius: var(--radius-3xl); }
.rounded-full { border-radius: var(--radius-full); }

/* Box shadow */
.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
.shadow-greta { box-shadow: var(--shadow-greta); }
.shadow-card { box-shadow: var(--shadow-card); }
.shadow-float { box-shadow: var(--shadow-float); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.shadow-xl { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }

/* Position */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.top-0 { top: 0; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }
.right-0 { right: 0; }
.left-1\/2 { left: 50%; }
.top-1\/2 { top: 50%; }
.transform { transform: translateZ(0); }
.-translate-x-1\/2 { transform: translateX(-50%); }
.-translate-y-1\/2 { transform: translateY(-50%); }
.transform { transform: translateZ(0); }

/* Z-index */
.z-10 { z-index: 10; }
.z-50 { z-index: 50; }

/* Overflow */
.overflow-hidden { overflow: hidden; }
.overflow-x-auto { overflow-x: auto; }

/* Transitions */
.transition-all { transition: all 150ms ease-in-out; }
.transition-colors { transition: color 150ms ease-in-out, background-color 150ms ease-in-out, border-color 150ms ease-in-out; }
.duration-200 { transition-duration: 200ms; }
.duration-300 { transition-duration: 300ms; }

/* Hover states */
.hover\:bg-greta-50:hover { background-color: var(--greta-50); }
.hover\:bg-greta-100:hover { background-color: var(--greta-100); }
.hover\:bg-greta-200:hover { background-color: var(--greta-200); }
.hover\:bg-red-100:hover { background-color: #fee2e2; }
.hover\:text-greta-700:hover { color: var(--greta-700); }
.hover\:shadow-card:hover { box-shadow: var(--shadow-card); }
.hover\:shadow-xl:hover { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }

/* Focus states */
.focus\:ring-2:focus { box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5); }
.focus\:ring-primary-500:focus { box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.5); }
.focus\:border-transparent:focus { border-color: transparent; }
.outline-none { outline: none; }

/* Other utilities */
.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }
.whitespace-nowrap { white-space: nowrap; }
.space-x-1 > * + * { margin-left: var(--space-1); }
.space-x-2 > * + * { margin-left: var(--space-2); }
.space-x-3 > * + * { margin-left: var(--space-3); }
.space-x-4 > * + * { margin-left: var(--space-4); }
.space-y-1 > * + * { margin-top: var(--space-1); }
.space-y-2 > * + * { margin-top: var(--space-2); }
.space-y-3 > * + * { margin-top: var(--space-3); }
.space-y-4 > * + * { margin-top: var(--space-4); }
.space-y-6 > * + * { margin-top: var(--space-6); }

/* Button styles */
button {
  border: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
}

input, textarea {
  font-family: inherit;
}

/* Custom animations */
.greta-pulse {
  animation: greta-pulse 2s infinite;
}

@keyframes greta-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
  }
}

.greta-bounce {
  animation: greta-bounce 2s infinite;
}

@keyframes greta-bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .pb-20 { padding-bottom: 5rem; }
  .safe-bottom { padding-bottom: env(safe-area-inset-bottom); }
}

/* Additional specific styles */
.last\:border-b-0:last-child { border-bottom: 0; }
.capitalize { text-transform: capitalize; }
.leading-relaxed { line-height: 1.625; }
.tracking-wider { letter-spacing: 0.05em; }
.font-mono { font-family: ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace; }

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* From/to gradients for animations */
.from-blue-500 { background: linear-gradient(to bottom right, #3b82f6, #1d4ed8); }
.to-blue-600 { }
.from-green-500 { background: linear-gradient(to bottom right, #22c55e, #16a34a); }
.to-green-600 { }
.from-purple-500 { background: linear-gradient(to bottom right, #a855f7, #9333ea); }
.to-purple-600 { }
.from-orange-500 { background: linear-gradient(to bottom right, #f97316, #ea580c); }
.to-orange-600 { }

/* Disabled states */
.disabled\:cursor-not-allowed:disabled { cursor: not-allowed; }
.disabled\:bg-greta-200:disabled { background-color: var(--greta-200); }
.disabled\:text-greta-500:disabled { color: var(--greta-500); }