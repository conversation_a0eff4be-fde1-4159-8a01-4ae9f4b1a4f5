import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useWallet } from '../../context/WalletContext';
import BottomNavigation from '../Navigation/BottomNavigation';
import { 
  ArrowLeft, Shield, Users, Globe, Clock, Phone, Mail, 
  CreditCard, Send, AlertTriangle, MapPin, Zap, DollarSign 
} from '../Icons/Icons';

const EmergencyDashboard = () => {
  const [emergencyContacts] = useState([
    { id: 1, name: '<PERSON>', phone: '******-0123', relationship: 'Family', canSendFunds: true },
    { id: 2, name: '<PERSON>', phone: '******-0456', relationship: 'Friend', canSendFunds: true },
    { id: 3, name: '<PERSON>', phone: '******-0789', relationship: 'Emergency Contact', canSendFunds: false }
  ]);
  
  const [isEmergencyMode, setIsEmergencyMode] = useState(false);
  const navigate = useNavigate();
  const { balance, requestEmergencyFunds } = useWallet();

  const emergencyServices = [
    {
      id: 'funds',
      title: 'Request Emergency Funds',
      description: 'Send fund request to your emergency contacts',
      icon: DollarSign,
      color: 'from-red-500 to-red-600',
      action: () => handleEmergencyFundRequest()
    },
    {
      id: 'location',
      title: 'Share Location',
      description: 'Send your current location to contacts',
      icon: MapPin,
      color: 'from-blue-500 to-blue-600',
      action: () => shareLocation()
    },
    {
      id: 'instant-card',
      title: 'Create Emergency Card',
      description: 'Instant virtual card for immediate use',
      icon: CreditCard,
      color: 'from-purple-500 to-purple-600',
      action: () => createEmergencyCard()
    },
    {
      id: 'sos',
      title: 'SOS Alert',
      description: 'Send emergency alert to all contacts',
      icon: AlertTriangle,
      color: 'from-orange-500 to-orange-600',
      action: () => sendSOSAlert()
    }
  ];

  const handleEmergencyFundRequest = () => {
    const amount = prompt('How much do you need? (USD)');
    const message = prompt('Emergency message (optional):') || 'I need emergency funds';
    
    if (amount && !isNaN(amount)) {
      emergencyContacts.forEach(contact => {
        if (contact.canSendFunds) {
          requestEmergencyFunds(contact, parseFloat(amount), message);
        }
      });
      alert(`Emergency fund request sent to ${emergencyContacts.filter(c => c.canSendFunds).length} contacts`);
    }
  };

  const shareLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition((position) => {
        const { latitude, longitude } = position.coords;
        const locationUrl = `https://maps.google.com/?q=${latitude},${longitude}`;
        alert(`Location shared: ${locationUrl}\nSent to all emergency contacts`);
      });
    }
  };

  const createEmergencyCard = () => {
    navigate('/cards');
  };

  const sendSOSAlert = () => {
    const confirmed = confirm('Send SOS alert to all emergency contacts?');
    if (confirmed) {
      alert('SOS alert sent to all contacts with your location and emergency card details');
    }
  };

  return (
    <div className="min-h-screen bg-greta-50 pb-20">
      {/* Header */}
      <motion.div
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="bg-red-600 text-white shadow-sm sticky top-0 z-10"
      >
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => navigate('/dashboard')}
                className="p-2 hover:bg-red-700 rounded-full transition-colors"
              >
                <ArrowLeft className="w-6 h-6 text-white" />
              </motion.button>
              <div>
                <h1 className="text-xl font-bold">Emergency Center</h1>
                <p className="text-red-100 text-sm">24/7 assistance available</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Shield className="w-6 h-6 text-red-200" />
              <span className="text-sm">Protected</span>
            </div>
          </div>
        </div>
      </motion.div>

      <div className="px-6 py-6 space-y-6">
        {/* Emergency Status */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className={`rounded-2xl p-6 shadow-card ${isEmergencyMode ? 'bg-red-100 border-2 border-red-300' : 'bg-green-100 border-2 border-green-300'}`}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className={`w-12 h-12 rounded-full flex items-center justify-center ${isEmergencyMode ? 'bg-red-200' : 'bg-green-200'}`}>
                {isEmergencyMode ? (
                  <AlertTriangle className="w-6 h-6 text-red-600" />
                ) : (
                  <Shield className="w-6 h-6 text-green-600" />
                )}
              </div>
              <div>
                <h3 className={`font-bold ${isEmergencyMode ? 'text-red-800' : 'text-green-800'}`}>
                  {isEmergencyMode ? 'Emergency Mode Active' : 'Status: Safe'}
                </h3>
                <p className={`text-sm ${isEmergencyMode ? 'text-red-600' : 'text-green-600'}`}>
                  {isEmergencyMode ? 'Emergency services activated' : 'All systems normal'}
                </p>
              </div>
            </div>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setIsEmergencyMode(!isEmergencyMode)}
              className={`px-4 py-2 rounded-xl font-semibold ${isEmergencyMode ? 'bg-red-600 text-white' : 'bg-green-600 text-white'}`}
            >
              {isEmergencyMode ? 'Deactivate' : 'Emergency'}
            </motion.button>
          </div>
        </motion.div>

        {/* Quick Access Balance */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-2xl p-6 shadow-greta"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-greta-600 text-sm">Emergency Funds Available</p>
              <h2 className="text-2xl font-bold text-greta-900">${balance.toFixed(2)}</h2>
              <p className="text-xs text-green-600 mt-1">✓ Instant access • No verification needed</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
              <Zap className="w-6 h-6 text-white" />
            </div>
          </div>
        </motion.div>

        {/* Emergency Services */}
        <div>
          <h3 className="text-lg font-semibold text-greta-900 mb-4">Emergency Services</h3>
          <div className="grid grid-cols-2 gap-4">
            {emergencyServices.map((service, index) => (
              <motion.button
                key={service.id}
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2 + index * 0.1 }}
                whileHover={{ scale: 1.02, y: -2 }}
                whileTap={{ scale: 0.98 }}
                onClick={service.action}
                className="bg-white rounded-2xl p-4 shadow-greta hover:shadow-card transition-all duration-300 text-left"
              >
                <div className={`w-12 h-12 bg-gradient-to-br ${service.color} rounded-xl flex items-center justify-center mb-3`}>
                  <service.icon className="w-6 h-6 text-white" />
                </div>
                <h4 className="font-semibold text-greta-900 mb-1">{service.title}</h4>
                <p className="text-sm text-greta-600">{service.description}</p>
              </motion.button>
            ))}
          </div>
        </div>

        {/* Emergency Contacts */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-greta-900">Emergency Contacts</h3>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="text-primary-600 font-medium text-sm flex items-center space-x-1"
            >
              <Users className="w-4 h-4" />
              <span>Manage</span>
            </motion.button>
          </div>
          <div className="space-y-3">
            {emergencyContacts.map((contact, index) => (
              <motion.div
                key={contact.id}
                initial={{ x: -20, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ delay: 0.3 + index * 0.1 }}
                className="bg-white rounded-xl p-4 shadow-greta"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <Users className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-greta-900">{contact.name}</h4>
                      <p className="text-sm text-greta-600">{contact.relationship}</p>
                      <p className="text-xs text-greta-500">{contact.phone}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {contact.canSendFunds && (
                      <span className="bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs font-medium">
                        Can Send Funds
                      </span>
                    )}
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      className="p-2 bg-greta-100 rounded-full"
                    >
                      <Phone className="w-4 h-4 text-greta-600" />
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* 24/7 Support */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="bg-gradient-to-r from-purple-100 to-blue-100 border border-purple-200 rounded-2xl p-6"
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
              <Clock className="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <h3 className="font-semibold text-purple-800">24/7 Support Available</h3>
              <p className="text-sm text-purple-600">Emergency assistance anytime, anywhere</p>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="bg-white bg-opacity-50 rounded-xl p-3 flex items-center space-x-2"
            >
              <Phone className="w-5 h-5 text-purple-600" />
              <span className="font-medium text-purple-800">Emergency Call</span>
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="bg-white bg-opacity-50 rounded-xl p-3 flex items-center space-x-2"
            >
              <Mail className="w-5 h-5 text-purple-600" />
              <span className="font-medium text-purple-800">Live Chat</span>
            </motion.button>
          </div>
        </motion.div>

        {/* Travel Safety Tips */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.6 }}
          className="bg-orange-50 border border-orange-200 rounded-2xl p-6"
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
              <Globe className="w-6 h-6 text-orange-600" />
            </div>
            <div>
              <h3 className="font-semibold text-orange-800">Travel Safety Tips</h3>
              <p className="text-sm text-orange-600">Stay safe while traveling</p>
            </div>
          </div>
          <ul className="space-y-2 text-sm text-orange-700">
            <li>• Keep multiple payment methods active</li>
            <li>• Share your location with trusted contacts</li>
            <li>• Have emergency funds on different cards</li>
            <li>• Save important numbers in your phone</li>
          </ul>
        </motion.div>
      </div>

      <BottomNavigation />
    </div>
  );
};

export default EmergencyDashboard;