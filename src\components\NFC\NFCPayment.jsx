import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useWallet } from '../../context/WalletContext';
import BottomNavigation from '../Navigation/BottomNavigation';
import { 
  ArrowLeft, 
  Smartphone, 
  Wifi, 
  Shield, 
  CheckCircle, 
  XCircle, 
  Radio, 
  CreditCard, 
  Fingerprint, 
  Zap 
} from '../Icons/Icons';

const NFCPayment = () => {
  const [nfcEnabled, setNfcEnabled] = useState(true);
  const [paymentStatus, setPaymentStatus] = useState('ready'); // ready, scanning, processing, success, error
  const [selectedCard, setSelectedCard] = useState(null);
  const [paymentAmount, setPaymentAmount] = useState(0);
  const [merchant, setMerchant] = useState('');
  const navigate = useNavigate();
  const { cards, virtualCards, addTransaction, balance } = useWallet();

  const allCards = [
    ...cards.map(card => ({ ...card, type: 'physical' })),
    ...virtualCards.filter(card => card.isActive).map(card => ({ ...card, type: 'virtual' }))
  ];

  useEffect(() => {
    // Simulate NFC detection
    if (paymentStatus === 'scanning') {
      const timer = setTimeout(() => {
        // Simulate merchant detection
        setMerchant('Starbucks Coffee');
        setPaymentAmount(4.75);
        setPaymentStatus('processing');
      }, 2000);
      return () => clearTimeout(timer);
    }

    if (paymentStatus === 'processing') {
      const timer = setTimeout(() => {
        // Simulate payment processing
        handlePaymentComplete();
      }, 1500);
      return () => clearTimeout(timer);
    }
  }, [paymentStatus]);

  const handlePaymentComplete = () => {
    addTransaction({
      type: 'payment',
      amount: -paymentAmount,
      description: merchant,
      merchant: merchant,
      method: 'NFC'
    });
    setPaymentStatus('success');
  };

  const startNFCPayment = () => {
    if (!selectedCard) {
      alert('Please select a payment method');
      return;
    }
    setPaymentStatus('scanning');
  };

  const resetPayment = () => {
    setPaymentStatus('ready');
    setPaymentAmount(0);
    setMerchant('');
  };

  const NFCStatusIndicator = () => (
    <motion.div
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      className="text-center mb-8"
    >
      <div className={`relative w-32 h-32 mx-auto mb-4 ${paymentStatus === 'scanning' ? 'greta-pulse' : ''}`}>
        <div className={`w-full h-full rounded-full flex items-center justify-center transition-all duration-500 ${
          nfcEnabled 
            ? paymentStatus === 'success' 
              ? 'bg-green-100 border-4 border-green-300'
              : paymentStatus === 'error'
              ? 'bg-red-100 border-4 border-red-300'
              : 'bg-blue-100 border-4 border-blue-300'
            : 'bg-greta-100 border-4 border-greta-300'
        }`}>
          {paymentStatus === 'success' ? (
            <CheckCircle className="w-16 h-16 text-green-600" />
          ) : paymentStatus === 'error' ? (
            <XCircle className="w-16 h-16 text-red-600" />
          ) : paymentStatus === 'scanning' ? (
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            >
              <Radio className="w-16 h-16 text-blue-600" />
            </motion.div>
          ) : (
            <Smartphone className="w-16 h-16 text-blue-600" />
          )}
        </div>

        {paymentStatus === 'scanning' && (
          <>
            <motion.div
              animate={{ scale: [1, 1.5], opacity: [0.7, 0] }}
              transition={{ duration: 1.5, repeat: Infinity }}
              className="absolute inset-0 border-4 border-blue-300 rounded-full"
            />
            <motion.div
              animate={{ scale: [1, 1.8], opacity: [0.5, 0] }}
              transition={{ duration: 1.5, repeat: Infinity, delay: 0.5 }}
              className="absolute inset-0 border-4 border-blue-300 rounded-full"
            />
          </>
        )}
      </div>

      <div>
        {paymentStatus === 'ready' && (
          <>
            <h2 className="text-xl font-bold text-greta-900 mb-2">NFC Payment Ready</h2>
            <p className="text-greta-600">Select a card and tap to pay at any NFC terminal</p>
          </>
        )}
        {paymentStatus === 'scanning' && (
          <>
            <h2 className="text-xl font-bold text-blue-600 mb-2">Scanning for Terminal</h2>
            <p className="text-greta-600">Hold your device near the payment terminal</p>
          </>
        )}
        {paymentStatus === 'processing' && (
          <>
            <h2 className="text-xl font-bold text-orange-600 mb-2">Processing Payment</h2>
            <p className="text-greta-600">Please wait while we process your payment</p>
          </>
        )}
        {paymentStatus === 'success' && (
          <>
            <h2 className="text-xl font-bold text-green-600 mb-2">Payment Successful!</h2>
            <p className="text-greta-600">Your payment of ${paymentAmount.toFixed(2)} was completed</p>
          </>
        )}
        {paymentStatus === 'error' && (
          <>
            <h2 className="text-xl font-bold text-red-600 mb-2">Payment Failed</h2>
            <p className="text-greta-600">Please try again or use a different payment method</p>
          </>
        )}
      </div>
    </motion.div>
  );

  return (
    <div className="min-h-screen bg-greta-50 pb-20">
      {/* Header */}
      <motion.div
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="bg-white shadow-sm sticky top-0 z-10"
      >
        <div className="px-6 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => navigate('/dashboard')}
              className="p-2 hover:bg-greta-100 rounded-full transition-colors"
            >
              <ArrowLeft className="w-6 h-6 text-greta-700" />
            </motion.button>
            <div>
              <h1 className="text-xl font-bold text-greta-900">NFC Payment</h1>
              <p className="text-sm text-greta-600">Contactless payments</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${nfcEnabled ? 'bg-green-500' : 'bg-red-500'}`} />
            <span className="text-sm text-greta-600">
              {nfcEnabled ? 'NFC On' : 'NFC Off'}
            </span>
          </div>
        </div>
      </motion.div>

      <div className="px-6 py-6">
        {/* NFC Status */}
        <NFCStatusIndicator />

        {/* Transaction Details */}
        <AnimatePresence>
          {(paymentStatus === 'processing' || paymentStatus === 'success') && merchant && (
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: -20, opacity: 0 }}
              className="bg-white rounded-2xl p-6 shadow-greta mb-6"
            >
              <div className="text-center">
                <h3 className="text-lg font-semibold text-greta-900 mb-2">{merchant}</h3>
                <p className="text-3xl font-bold text-greta-900">${paymentAmount.toFixed(2)}</p>
                <div className="flex items-center justify-center space-x-2 mt-2 text-sm text-greta-600">
                  <Fingerprint className="w-4 h-4" />
                  <span>Authenticated with biometrics</span>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Payment Methods */}
        {paymentStatus === 'ready' && (
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            className="mb-6"
          >
            <h3 className="text-lg font-semibold text-greta-900 mb-4">Select Payment Method</h3>
            <div className="space-y-3">
              {allCards.map((card) => (
                <motion.button
                  key={`${card.type}-${card.id}`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => setSelectedCard(card)}
                  className={`w-full p-4 rounded-xl border-2 transition-all ${
                    selectedCard?.id === card.id
                      ? 'border-primary-500 bg-primary-50'
                      : 'border-greta-300 bg-white'
                  }`}
                >
                  <div className="flex items-center space-x-4">
                    <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                      card.type === 'virtual' 
                        ? `bg-gradient-to-br ${card.color}` 
                        : 'bg-gradient-card'
                    }`}>
                      <CreditCard className="w-6 h-6 text-white" />
                    </div>
                    <div className="flex-1 text-left">
                      <h4 className="font-semibold text-greta-900">
                        {card.name || `${card.brand} Card`}
                      </h4>
                      <p className="text-sm text-greta-600">
                        {card.type === 'virtual' ? 'Virtual' : card.type} • 
                        {card.number ? ` ${card.number.slice(-4)}` : ` •••• ${card.last4}`}
                      </p>
                    </div>
                    {card.type === 'virtual' && (
                      <div className="text-right">
                        <p className="text-sm font-semibold text-greta-900">
                          ${card.balance?.toFixed(2)}
                        </p>
                      </div>
                    )}
                    {selectedCard?.id === card.id && (
                      <CheckCircle className="w-5 h-5 text-primary-600" />
                    )}
                  </div>
                </motion.button>
              ))}
            </div>
          </motion.div>
        )}

        {/* Security Features */}
        {paymentStatus === 'ready' && (
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="bg-white rounded-2xl p-6 shadow-greta mb-6"
          >
            <h3 className="text-lg font-semibold text-greta-900 mb-4">Security Features</h3>
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                  <Shield className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <h4 className="font-medium text-greta-900">Tokenized Payments</h4>
                  <p className="text-sm text-greta-600">Your card details are never shared</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <Fingerprint className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h4 className="font-medium text-greta-900">Biometric Authentication</h4>
                  <p className="text-sm text-greta-600">Secure with fingerprint or face ID</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                  <Zap className="w-5 h-5 text-purple-600" />
                </div>
                <div>
                  <h4 className="font-medium text-greta-900">Instant Processing</h4>
                  <p className="text-sm text-greta-600">Payments complete in seconds</p>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Action Buttons */}
        <div className="space-y-3">
          {paymentStatus === 'ready' && (
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={startNFCPayment}
              disabled={!selectedCard}
              className={`w-full py-4 rounded-2xl font-semibold transition-all ${
                selectedCard
                  ? 'bg-gradient-greta text-white shadow-lg hover:shadow-xl'
                  : 'bg-greta-200 text-greta-500 cursor-not-allowed'
              }`}
            >
              <div className="flex items-center justify-center space-x-2">
                <Wifi className="w-5 h-5" />
                <span>Start NFC Payment</span>
              </div>
            </motion.button>
          )}

          {paymentStatus === 'success' && (
            <>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => navigate('/dashboard')}
                className="w-full bg-gradient-greta text-white py-4 rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all"
              >
                Done
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={resetPayment}
                className="w-full bg-white border border-greta-300 text-greta-700 py-4 rounded-2xl font-semibold hover:bg-greta-50 transition-all"
              >
                Make Another Payment
              </motion.button>
            </>
          )}

          {paymentStatus === 'error' && (
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={resetPayment}
              className="w-full bg-gradient-greta text-white py-4 rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all"
            >
              Try Again
            </motion.button>
          )}
        </div>
      </div>

      <BottomNavigation />
    </div>
  );
};

export default NFCPayment;