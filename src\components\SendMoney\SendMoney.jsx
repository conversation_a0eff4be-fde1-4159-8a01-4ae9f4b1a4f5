import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useWallet } from '../../context/WalletContext';
import BottomNavigation from '../Navigation/BottomNavigation';
import { 
  ArrowLeft, 
  Send, 
  User, 
  Phone, 
  Mail, 
  DollarSign, 
  Check, 
  Clock, 
  Smartphone 
} from '../Icons/Icons';

const SendMoney = () => {
  const [step, setStep] = useState('recipient'); // recipient, amount, confirm, success
  const [recipient, setRecipient] = useState('');
  const [amount, setAmount] = useState('');
  const [note, setNote] = useState('');
  const [paymentMethod, setPaymentMethod] = useState('googlepay');
  const navigate = useNavigate();
  const { addTransaction, balance } = useWallet();

  const quickAmounts = [25, 50, 100, 200];

  const recentContacts = [
    {
      id: '1',
      name: '<PERSON>',
      phone: '+****************',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face'
    },
    {
      id: '2',
      name: 'Mike Chen',
      phone: '+****************',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'
    },
    {
      id: '3',
      name: 'Emily Davis',
      phone: '+****************',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face'
    }
  ];

  const handleSendMoney = () => {
    const transactionAmount = parseFloat(amount);
    if (transactionAmount > balance) {
      alert('Insufficient balance');
      return;
    }

    addTransaction({
      type: 'transfer',
      amount: -transactionAmount,
      description: `Sent to ${recipient}`,
      recipient: recipient,
      method: 'Google Pay',
      note: note
    });

    setStep('success');
  };

  const renderRecipientStep = () => (
    <motion.div
      initial={{ x: 300, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      exit={{ x: -300, opacity: 0 }}
      className="space-y-6"
    >
      <div>
        <h2 className="text-2xl font-bold text-greta-900 mb-2">Send Money</h2>
        <p className="text-greta-600">Choose a recipient or enter details</p>
      </div>

      {/* Search Input */}
      <div className="relative">
        <input
          type="text"
          value={recipient}
          onChange={(e) => setRecipient(e.target.value)}
          placeholder="Enter name, phone, or email"
          className="w-full pl-12 pr-4 py-4 bg-white border border-greta-300 rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-transparent outline-none"
        />
        <User className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-greta-400" />
      </div>

      {/* Recent Contacts */}
      <div>
        <h3 className="text-lg font-semibold text-greta-900 mb-4">Recent Contacts</h3>
        <div className="space-y-3">
          {recentContacts.map((contact) => (
            <motion.button
              key={contact.id}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => {
                setRecipient(contact.name);
                setStep('amount');
              }}
              className="w-full bg-white rounded-xl p-4 shadow-greta flex items-center space-x-4 text-left hover:shadow-card transition-all"
            >
              <img
                src={contact.avatar}
                alt={contact.name}
                className="w-12 h-12 rounded-full"
              />
              <div className="flex-1">
                <h4 className="font-semibold text-greta-900">{contact.name}</h4>
                <p className="text-sm text-greta-600">{contact.phone}</p>
              </div>
              <Send className="w-5 h-5 text-greta-400" />
            </motion.button>
          ))}
        </div>
      </div>

      {/* Continue Button */}
      <motion.button
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        onClick={() => recipient && setStep('amount')}
        disabled={!recipient}
        className={`w-full py-4 rounded-2xl font-semibold transition-all ${
          recipient
            ? 'bg-gradient-greta text-white shadow-lg hover:shadow-xl'
            : 'bg-greta-200 text-greta-500 cursor-not-allowed'
        }`}
      >
        Continue
      </motion.button>
    </motion.div>
  );

  const renderAmountStep = () => (
    <motion.div
      initial={{ x: 300, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      exit={{ x: -300, opacity: 0 }}
      className="space-y-6"
    >
      <div>
        <h2 className="text-2xl font-bold text-greta-900 mb-2">Enter Amount</h2>
        <p className="text-greta-600">Sending to {recipient}</p>
      </div>

      {/* Amount Input */}
      <div className="text-center py-8">
        <div className="relative inline-block">
          <DollarSign className="absolute left-0 top-1/2 transform -translate-y-1/2 w-8 h-8 text-greta-400" />
          <input
            type="number"
            value={amount}
            onChange={(e) => setAmount(e.target.value)}
            placeholder="0.00"
            className="text-5xl font-bold text-greta-900 bg-transparent border-none outline-none pl-10 text-center w-64"
          />
        </div>
        <p className="text-sm text-greta-600 mt-2">Available balance: ${balance.toFixed(2)}</p>
      </div>

      {/* Quick Amounts */}
      <div className="grid grid-cols-4 gap-3">
        {quickAmounts.map((quickAmount) => (
          <motion.button
            key={quickAmount}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setAmount(quickAmount.toString())}
            className="py-3 bg-white border border-greta-300 rounded-xl font-semibold text-greta-700 hover:bg-greta-50 transition-all"
          >
            ${quickAmount}
          </motion.button>
        ))}
      </div>

      {/* Note */}
      <div>
        <label className="block text-sm font-medium text-greta-700 mb-2">
          Add a note (optional)
        </label>
        <input
          type="text"
          value={note}
          onChange={(e) => setNote(e.target.value)}
          placeholder="What's this for?"
          className="w-full px-4 py-3 bg-white border border-greta-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent outline-none"
        />
      </div>

      {/* Payment Method */}
      <div>
        <label className="block text-sm font-medium text-greta-700 mb-3">
          Payment Method
        </label>
        <div className="space-y-2">
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => setPaymentMethod('googlepay')}
            className={`w-full p-4 rounded-xl border-2 flex items-center space-x-3 transition-all ${
              paymentMethod === 'googlepay'
                ? 'border-primary-500 bg-primary-50'
                : 'border-greta-300 bg-white'
            }`}
          >
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
              <Smartphone className="w-5 h-5 text-white" />
            </div>
            <div className="flex-1 text-left">
              <h4 className="font-semibold text-greta-900">Google Pay</h4>
              <p className="text-sm text-greta-600">Instant transfer</p>
            </div>
            {paymentMethod === 'googlepay' && (
              <Check className="w-5 h-5 text-primary-600" />
            )}
          </motion.button>
        </div>
      </div>

      {/* Continue Button */}
      <motion.button
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        onClick={() => amount && setStep('confirm')}
        disabled={!amount || parseFloat(amount) <= 0}
        className={`w-full py-4 rounded-2xl font-semibold transition-all ${
          amount && parseFloat(amount) > 0
            ? 'bg-gradient-greta text-white shadow-lg hover:shadow-xl'
            : 'bg-greta-200 text-greta-500 cursor-not-allowed'
        }`}
      >
        Continue
      </motion.button>
    </motion.div>
  );

  const renderConfirmStep = () => (
    <motion.div
      initial={{ x: 300, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      exit={{ x: -300, opacity: 0 }}
      className="space-y-6"
    >
      <div>
        <h2 className="text-2xl font-bold text-greta-900 mb-2">Confirm Transfer</h2>
        <p className="text-greta-600">Review your transaction details</p>
      </div>

      {/* Transaction Summary */}
      <div className="bg-white rounded-2xl p-6 shadow-greta">
        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-gradient-greta rounded-full flex items-center justify-center mx-auto mb-4">
            <Send className="w-8 h-8 text-white" />
          </div>
          <h3 className="text-3xl font-bold text-greta-900">${amount}</h3>
          <p className="text-greta-600">to {recipient}</p>
        </div>

        <div className="space-y-4 border-t border-greta-200 pt-4">
          <div className="flex justify-between">
            <span className="text-greta-600">Amount</span>
            <span className="font-semibold text-greta-900">${amount}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-greta-600">Fee</span>
            <span className="font-semibold text-green-600">Free</span>
          </div>
          <div className="flex justify-between">
            <span className="text-greta-600">Payment Method</span>
            <span className="font-semibold text-greta-900">Google Pay</span>
          </div>
          {note && (
            <div className="flex justify-between">
              <span className="text-greta-600">Note</span>
              <span className="font-semibold text-greta-900">{note}</span>
            </div>
          )}
          <div className="border-t border-greta-200 pt-4">
            <div className="flex justify-between">
              <span className="font-semibold text-greta-900">Total</span>
              <span className="font-bold text-greta-900">${amount}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Security Notice */}
      <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <Clock className="w-4 h-4 text-blue-600" />
          </div>
          <div>
            <h4 className="font-semibold text-blue-800">Instant Transfer</h4>
            <p className="text-sm text-blue-600">Money will arrive immediately via Google Pay</p>
          </div>
        </div>
      </div>

      {/* Send Button */}
      <motion.button
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        onClick={handleSendMoney}
        className="w-full bg-gradient-greta text-white py-4 rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all"
      >
        Send ${amount}
      </motion.button>
    </motion.div>
  );

  const renderSuccessStep = () => (
    <motion.div
      initial={{ scale: 0.9, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      className="text-center space-y-6 py-8"
    >
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: 0.2, type: "spring", stiffness: 260, damping: 20 }}
        className="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto"
      >
        <Check className="w-12 h-12 text-green-600" />
      </motion.div>

      <div>
        <h2 className="text-2xl font-bold text-greta-900 mb-2">Money Sent!</h2>
        <p className="text-greta-600">Your transfer to {recipient} was successful</p>
      </div>

      <div className="bg-white rounded-2xl p-6 shadow-greta">
        <h3 className="text-3xl font-bold text-greta-900 mb-2">${amount}</h3>
        <p className="text-greta-600 mb-4">sent via Google Pay</p>
        <div className="text-sm text-greta-500">
          Transaction ID: GP{Date.now().toString().slice(-6)}
        </div>
      </div>

      <div className="space-y-3">
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => navigate('/dashboard')}
          className="w-full bg-gradient-greta text-white py-4 rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all"
        >
          Done
        </motion.button>
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => {
            setStep('recipient');
            setRecipient('');
            setAmount('');
            setNote('');
          }}
          className="w-full bg-white border border-greta-300 text-greta-700 py-4 rounded-2xl font-semibold hover:bg-greta-50 transition-all"
        >
          Send Another
        </motion.button>
      </div>
    </motion.div>
  );

  return (
    <div className="min-h-screen bg-greta-50 pb-20">
      {/* Header */}
      <motion.div
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="bg-white shadow-sm sticky top-0 z-10"
      >
        <div className="px-6 py-4 flex items-center space-x-4">
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => {
              if (step === 'recipient') {
                navigate('/dashboard');
              } else if (step === 'amount') {
                setStep('recipient');
              } else if (step === 'confirm') {
                setStep('amount');
              }
            }}
            className="p-2 hover:bg-greta-100 rounded-full transition-colors"
          >
            <ArrowLeft className="w-6 h-6 text-greta-700" />
          </motion.button>
          <div className="flex-1">
            <div className="flex space-x-2">
              {['recipient', 'amount', 'confirm'].map((stepName, index) => (
                <div
                  key={stepName}
                  className={`h-1 flex-1 rounded-full transition-all duration-300 ${
                    (step === stepName) ||
                    (step === 'amount' && stepName === 'recipient') ||
                    (step === 'confirm' && (stepName === 'recipient' || stepName === 'amount'))
                      ? 'bg-primary-600'
                      : 'bg-greta-200'
                  }`}
                />
              ))}
            </div>
          </div>
        </div>
      </motion.div>

      {/* Content */}
      <div className="px-6 py-6">
        {step === 'recipient' && renderRecipientStep()}
        {step === 'amount' && renderAmountStep()}
        {step === 'confirm' && renderConfirmStep()}
        {step === 'success' && renderSuccessStep()}
      </div>

      <BottomNavigation />
    </div>
  );
};

export default SendMoney;