import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useWallet } from '../../context/WalletContext';
import { useAuth } from '../../context/AuthContext';
import BottomNavigation from '../Navigation/BottomNavigation';
import { 
  Wallet, Send, CreditCard, Smartphone, Plus, Eye, EyeOff, 
  ArrowUpRight, ArrowDownLeft, TrendingUp, Bell, Settings, 
  Shield, Globe, Zap, Users 
} from '../Icons/Icons';

const Dashboard = () => {
  const [balanceVisible, setBalanceVisible] = useState(true);
  const { balance, cards, virtualCards, transactions } = useWallet();
  const { user } = useAuth();
  const navigate = useNavigate();

  const recentTransactions = transactions.slice(0, 5);

  const quickActions = [
    {
      id: 'send',
      title: 'Send Money',
      subtitle: 'Instant Transfer',
      icon: Send,
      color: 'bg-blue-500',
      gradient: 'from-blue-500 to-blue-600',
      route: '/send'
    },
    {
      id: 'nfc',
      title: 'NFC Pay',
      subtitle: 'Tap to Pay',
      icon: Smartphone,
      color: 'bg-green-500',
      gradient: 'from-green-500 to-green-600',
      route: '/nfc'
    },
    {
      id: 'cards',
      title: 'Virtual Cards',
      subtitle: 'No Verification',
      icon: CreditCard,
      color: 'bg-purple-500',
      gradient: 'from-purple-500 to-purple-600',
      route: '/cards'
    },
    {
      id: 'emergency',
      title: 'Emergency',
      subtitle: '24/7 Support',
      icon: Shield,
      color: 'bg-red-500',
      gradient: 'from-red-500 to-red-600',
      route: '/emergency'
    }
  ];

  const travelerFeatures = [
    {
      id: 'telegram',
      title: 'Telegram Bot',
      description: 'Send money via Telegram',
      icon: Users,
      route: '/telegram'
    },
    {
      id: 'instant-load',
      title: 'Instant Loading',
      description: 'Load funds anytime',
      icon: Zap,
      action: () => {
        const amount = prompt('Amount to load (USD):');
        if (amount && !isNaN(amount)) {
          // Simulate instant loading
          alert(`$${amount} loaded instantly!\nNo verification required.`);
        }
      }
    },
    {
      id: 'global-access',
      title: 'Global Access',
      description: 'Works worldwide',
      icon: Globe,
      action: () => alert('Available in 195+ countries')
    }
  ];

  const getTransactionIcon = (type) => {
    switch (type) {
      case 'payment':
        return <ArrowUpRight className="w-5 h-5 text-red-500" />;
      case 'transfer':
        return <Send className="w-5 h-5 text-blue-500" />;
      case 'topup':
      case 'emergency':
        return <ArrowDownLeft className="w-5 h-5 text-green-500" />;
      default:
        return <Wallet className="w-5 h-5 text-greta-500" />;
    }
  };

  return (
    <div className="min-h-screen bg-greta-50 pb-20">
      {/* Header */}
      <motion.div
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="bg-white shadow-sm"
      >
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <img
                src={user?.avatar}
                alt={user?.name}
                className="w-10 h-10 rounded-full border-2 border-primary-200"
              />
              <div>
                <h1 className="text-lg font-semibold text-greta-900">
                  Good morning, {user?.name?.split(' ')[0]}
                  {user?.isGuest && (
                    <span className="ml-2 text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full">
                      Guest
                    </span>
                  )}
                </h1>
                <p className="text-sm text-greta-600">
                  {user?.isGuest ? 'Demo Mode • Perfect for travelers' : 'Ready for your next adventure'}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="p-2 bg-greta-100 rounded-full"
              >
                <Bell className="w-5 h-5 text-greta-600" />
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => navigate('/settings')}
                className="p-2 bg-greta-100 rounded-full"
              >
                <Settings className="w-5 h-5 text-greta-600" />
              </motion.button>
            </div>
          </div>
        </div>
      </motion.div>

      <div className="px-6 py-6 space-y-6">
        {/* Traveler Welcome Banner */}
        {user?.isGuest && (
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            className="bg-gradient-to-r from-blue-50 to-green-50 border border-blue-200 rounded-2xl p-4"
          >
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <Globe className="w-5 h-5 text-blue-600" />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-blue-800">Perfect for Travelers</h3>
                <p className="text-sm text-blue-600">No verification • Instant cards • 24/7 support</p>
              </div>
            </div>
          </motion.div>
        )}

        {/* Balance Card */}
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ delay: 0.1 }}
          className="bg-gradient-greta rounded-3xl p-6 text-white shadow-card"
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <p className="text-white/80 text-sm font-medium">
                Available Balance
              </p>
              <div className="flex items-center space-x-3">
                <h2 className="text-3xl font-bold">
                  {balanceVisible ? `$${balance.toFixed(2)}` : '••••••'}
                </h2>
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={() => setBalanceVisible(!balanceVisible)}
                  className="p-1"
                >
                  {balanceVisible ? (
                    <EyeOff className="w-5 h-5 text-white/80" />
                  ) : (
                    <Eye className="w-5 h-5 text-white/80" />
                  )}
                </motion.button>
              </div>
            </div>
            <div className="text-right">
              <div className="flex items-center space-x-1 text-white/80 text-sm">
                <Zap className="w-4 h-4" />
                <span>Instant</span>
              </div>
              <p className="text-white/60 text-xs">No verification needed</p>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/60 text-xs">Active Cards</p>
              <p className="text-white font-semibold">
                {cards.length + virtualCards.filter(c => c.isActive).length}
              </p>
            </div>
            <div>
              <p className="text-white/60 text-xs">Ready 24/7</p>
              <p className="text-white font-semibold">✓ Available</p>
            </div>
          </div>
        </motion.div>

        {/* Quick Actions */}
        <div>
          <h3 className="text-lg font-semibold text-greta-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-2 gap-4">
            {quickActions.map((action, index) => (
              <motion.button
                key={action.id}
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2 + index * 0.1 }}
                whileHover={{ scale: 1.02, y: -2 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => action.route ? navigate(action.route) : action.action?.()}
                className="bg-white rounded-2xl p-4 shadow-greta hover:shadow-card transition-all duration-300"
              >
                <div className={`w-12 h-12 bg-gradient-to-br ${action.gradient} rounded-xl flex items-center justify-center mb-3`}>
                  <action.icon className="w-6 h-6 text-white" />
                </div>
                <h4 className="font-semibold text-greta-900 text-left">{action.title}</h4>
                <p className="text-sm text-greta-600 text-left">{action.subtitle}</p>
              </motion.button>
            ))}
          </div>
        </div>

        {/* Traveler Features */}
        <div>
          <h3 className="text-lg font-semibold text-greta-900 mb-4">Traveler Features</h3>
          <div className="space-y-3">
            {travelerFeatures.map((feature, index) => (
              <motion.button
                key={feature.id}
                initial={{ x: -20, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ delay: 0.3 + index * 0.1 }}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => feature.route ? navigate(feature.route) : feature.action?.()}
                className="w-full bg-white rounded-xl p-4 shadow-greta hover:shadow-card transition-all text-left"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <feature.icon className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-greta-900">{feature.title}</h4>
                    <p className="text-sm text-greta-600">{feature.description}</p>
                  </div>
                </div>
              </motion.button>
            ))}
          </div>
        </div>

        {/* Recent Transactions */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-greta-900">Recent Activity</h3>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => navigate('/transactions')}
              className="text-primary-600 font-medium text-sm"
            >
              View All
            </motion.button>
          </div>
          <div className="bg-white rounded-2xl shadow-greta overflow-hidden">
            {recentTransactions.map((transaction, index) => (
              <motion.div
                key={transaction.id}
                initial={{ x: -20, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ delay: 0.4 + index * 0.1 }}
                className="p-4 border-b border-greta-100 last:border-b-0 flex items-center justify-between"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-greta-100 rounded-full flex items-center justify-center">
                    {getTransactionIcon(transaction.type)}
                  </div>
                  <div>
                    <h4 className="font-medium text-greta-900">{transaction.description}</h4>
                    <p className="text-sm text-greta-600">
                      {transaction.date.toLocaleDateString()} • {transaction.method}
                      {transaction.location && ` • ${transaction.location}`}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className={`font-semibold ${transaction.amount > 0 ? 'text-green-600' : 'text-greta-900'}`}>
                    {transaction.amount > 0 ? '+' : ''}${Math.abs(transaction.amount).toFixed(2)}
                  </p>
                  <p className="text-xs text-greta-500 capitalize">{transaction.status}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      <BottomNavigation />
    </div>
  );
};

export default Dashboard;