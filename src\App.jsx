import React from 'react';
import { HashRouter as Router, Routes, Route } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { WalletProvider } from './context/WalletContext';
import { AuthProvider } from './context/AuthContext';

// Components
import Onboarding from './components/Onboarding/Onboarding';
import Dashboard from './components/Dashboard/Dashboard';
import SendMoney from './components/SendMoney/SendMoney';
import VirtualCardManager from './components/VirtualCards/VirtualCardManager';
import NFCPayment from './components/NFC/NFCPayment';
import TransactionHistory from './components/Transactions/TransactionHistory';
import Settings from './components/Settings/Settings';
import EmergencyDashboard from './components/EmergencyFeatures/EmergencyDashboard';
import TelegramIntegration from './components/TelegramBot/TelegramIntegration';

import './App.css';

function App() {
  return (
    <AuthProvider>
      <WalletProvider>
        <Router>
          <div className="app-container font-gilroy bg-gray-50 min-h-screen">
            <AnimatePresence mode="wait">
              <Routes>
                <Route path="/" element={<Onboarding />} />
                <Route path="/dashboard" element={<Dashboard />} />
                <Route path="/send" element={<SendMoney />} />
                <Route path="/cards" element={<VirtualCardManager />} />
                <Route path="/nfc" element={<NFCPayment />} />
                <Route path="/transactions" element={<TransactionHistory />} />
                <Route path="/settings" element={<Settings />} />
                <Route path="/emergency" element={<EmergencyDashboard />} />
                <Route path="/telegram" element={<TelegramIntegration />} />
              </Routes>
            </AnimatePresence>
          </div>
        </Router>
      </WalletProvider>
    </AuthProvider>
  );
}

export default App;