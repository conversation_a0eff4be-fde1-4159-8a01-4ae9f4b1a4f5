import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useWallet } from '../../context/WalletContext';
import BottomNavigation from '../Navigation/BottomNavigation';
import { 
  ArrowLeft, Send, Users, Globe, Copy, Settings, 
  CheckCircle, Clock, DollarSign, Smartphone 
} from '../Icons/Icons';

const TelegramIntegration = () => {
  const [botToken] = useState('@GretaWalletBot');
  const [isConnected, setIsConnected] = useState(false);
  const [telegramUsername, setTelegramUsername] = useState('');
  const [recentBotTransactions] = useState([
    { id: 1, type: 'received', amount: 50, from: '@john_doe', time: '2 min ago', status: 'completed' },
    { id: 2, type: 'sent', amount: 25, to: '@sarah_m', time: '1 hour ago', status: 'completed' },
    { id: 3, type: 'request', amount: 100, from: '@mike_travel', time: '3 hours ago', status: 'pending' }
  ]);

  const navigate = useNavigate();
  const { balance, addTransaction } = useWallet();

  const connectTelegram = () => {
    // Simulate Telegram connection
    const username = prompt('Enter your Telegram username (without @):');
    if (username) {
      setTelegramUsername(username);
      setIsConnected(true);
      alert(`Connected to Telegram as @${username}\nBot commands are now active!`);
    }
  };

  const sendPaymentLink = () => {
    const amount = prompt('Amount to request (USD):');
    const recipient = prompt('Recipient Telegram username (without @):');
    
    if (amount && recipient) {
      const paymentLink = `https://t.me/GretaWalletBot?start=pay_${amount}_${recipient}`;
      navigator.clipboard.writeText(paymentLink);
      alert(`Payment link created and copied:\n${paymentLink}\n\nSend this to @${recipient} to request $${amount}`);
    }
  };

  const botCommands = [
    { command: '/balance', description: 'Check your wallet balance' },
    { command: '/send [amount] [@username]', description: 'Send money to another user' },
    { command: '/request [amount] [@username]', description: 'Request money from another user' },
    { command: '/history', description: 'View recent transactions' },
    { command: '/card', description: 'Get virtual card details for payments' },
    { command: '/emergency', description: 'Access emergency features' }
  ];

  const quickActions = [
    {
      id: 'send-link',
      title: 'Send Payment Link',
      description: 'Create shareable payment link',
      icon: Send,
      color: 'from-blue-500 to-blue-600',
      action: sendPaymentLink
    },
    {
      id: 'group-pay',
      title: 'Group Payment',
      description: 'Split bills with friends',
      icon: Users,
      color: 'from-green-500 to-green-600',
      action: () => alert('Group payment feature coming soon!')
    },
    {
      id: 'travel-assist',
      title: 'Travel Assistant',
      description: 'Location-based payments',
      icon: Globe,
      color: 'from-purple-500 to-purple-600',
      action: () => navigate('/emergency')
    }
  ];

  return (
    <div className="min-h-screen bg-greta-50 pb-20">
      {/* Header */}
      <motion.div
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="bg-blue-600 text-white shadow-sm sticky top-0 z-10"
      >
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => navigate('/dashboard')}
                className="p-2 hover:bg-blue-700 rounded-full transition-colors"
              >
                <ArrowLeft className="w-6 h-6 text-white" />
              </motion.button>
              <div>
                <h1 className="text-xl font-bold">Telegram Payments</h1>
                <p className="text-blue-100 text-sm">Send money through Telegram</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`} />
              <span className="text-sm">{isConnected ? 'Connected' : 'Offline'}</span>
            </div>
          </div>
        </div>
      </motion.div>

      <div className="px-6 py-6 space-y-6">
        {/* Connection Status */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className={`rounded-2xl p-6 shadow-card ${isConnected ? 'bg-green-50 border-2 border-green-200' : 'bg-orange-50 border-2 border-orange-200'}`}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className={`w-12 h-12 rounded-full flex items-center justify-center ${isConnected ? 'bg-green-100' : 'bg-orange-100'}`}>
                <Smartphone className={`w-6 h-6 ${isConnected ? 'text-green-600' : 'text-orange-600'}`} />
              </div>
              <div>
                <h3 className={`font-bold ${isConnected ? 'text-green-800' : 'text-orange-800'}`}>
                  {isConnected ? `Connected as @${telegramUsername}` : 'Not Connected'}
                </h3>
                <p className={`text-sm ${isConnected ? 'text-green-600' : 'text-orange-600'}`}>
                  {isConnected ? 'Bot is active and ready' : 'Connect to start using bot payments'}
                </p>
              </div>
            </div>
            {!isConnected && (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={connectTelegram}
                className="bg-blue-600 text-white px-4 py-2 rounded-xl font-semibold"
              >
                Connect
              </motion.button>
            )}
          </div>
        </motion.div>

        {/* Bot Information */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-2xl p-6 shadow-greta"
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <h3 className="text-lg font-semibold text-greta-900">Greta Wallet Bot</h3>
              <div className="flex items-center space-x-2">
                <p className="text-greta-600">{botToken}</p>
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={() => {
                    navigator.clipboard.writeText(botToken);
                    alert('Bot username copied!');
                  }}
                  className="p-1"
                >
                  <Copy className="w-4 h-4 text-greta-500" />
                </motion.button>
              </div>
            </div>
            <div className="text-center">
              <p className="text-sm text-greta-600">Available Balance</p>
              <p className="text-xl font-bold text-greta-900">${balance.toFixed(2)}</p>
            </div>
          </div>

          <div className="bg-blue-50 rounded-xl p-4">
            <h4 className="font-semibold text-blue-800 mb-2">How to Start:</h4>
            <ol className="text-sm text-blue-700 space-y-1">
              <li>1. Open Telegram and search for {botToken}</li>
              <li>2. Start a chat with the bot</li>
              <li>3. Type /start to activate</li>
              <li>4. Use commands below to send/receive money</li>
            </ol>
          </div>
        </motion.div>

        {/* Quick Actions */}
        {isConnected && (
          <div>
            <h3 className="text-lg font-semibold text-greta-900 mb-4">Quick Actions</h3>
            <div className="grid grid-cols-1 gap-4">
              {quickActions.map((action, index) => (
                <motion.button
                  key={action.id}
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.2 + index * 0.1 }}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={action.action}
                  className="bg-white rounded-2xl p-4 shadow-greta hover:shadow-card transition-all duration-300 text-left"
                >
                  <div className="flex items-center space-x-4">
                    <div className={`w-12 h-12 bg-gradient-to-br ${action.color} rounded-xl flex items-center justify-center`}>
                      <action.icon className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-greta-900">{action.title}</h4>
                      <p className="text-sm text-greta-600">{action.description}</p>
                    </div>
                  </div>
                </motion.button>
              ))}
            </div>
          </div>
        )}

        {/* Bot Commands */}
        <div>
          <h3 className="text-lg font-semibold text-greta-900 mb-4">Bot Commands</h3>
          <div className="bg-white rounded-2xl shadow-greta overflow-hidden">
            {botCommands.map((cmd, index) => (
              <div
                key={index}
                className="p-4 border-b border-greta-100 last:border-b-0 hover:bg-greta-50 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <code className="text-sm font-mono bg-greta-100 px-2 py-1 rounded text-blue-600">
                      {cmd.command}
                    </code>
                    <p className="text-sm text-greta-600 mt-1">{cmd.description}</p>
                  </div>
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => {
                      navigator.clipboard.writeText(cmd.command.split(' ')[0]);
                      alert('Command copied!');
                    }}
                    className="p-2 hover:bg-greta-100 rounded-full"
                  >
                    <Copy className="w-4 h-4 text-greta-500" />
                  </motion.button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Bot Transactions */}
        {isConnected && (
          <div>
            <h3 className="text-lg font-semibold text-greta-900 mb-4">Recent Bot Activity</h3>
            <div className="bg-white rounded-2xl shadow-greta overflow-hidden">
              {recentBotTransactions.map((transaction, index) => (
                <motion.div
                  key={transaction.id}
                  initial={{ x: -20, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ delay: 0.3 + index * 0.1 }}
                  className="p-4 border-b border-greta-100 last:border-b-0 flex items-center justify-between"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      {transaction.type === 'received' && <ArrowLeft className="w-5 h-5 text-green-600 transform rotate-180" />}
                      {transaction.type === 'sent' && <Send className="w-5 h-5 text-blue-600" />}
                      {transaction.type === 'request' && <Clock className="w-5 h-5 text-orange-600" />}
                    </div>
                    <div>
                      <h4 className="font-medium text-greta-900">
                        {transaction.type === 'received' && `Received from ${transaction.from}`}
                        {transaction.type === 'sent' && `Sent to ${transaction.to}`}
                        {transaction.type === 'request' && `Request from ${transaction.from}`}
                      </h4>
                      <p className="text-sm text-greta-600">{transaction.time}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`font-semibold ${
                      transaction.type === 'received' ? 'text-green-600' : 
                      transaction.type === 'sent' ? 'text-greta-900' : 'text-orange-600'
                    }`}>
                      {transaction.type === 'received' ? '+' : transaction.type === 'sent' ? '-' : ''}${transaction.amount}
                    </p>
                    <div className="flex items-center space-x-1">
                      {transaction.status === 'completed' && <CheckCircle className="w-3 h-3 text-green-500" />}
                      {transaction.status === 'pending' && <Clock className="w-3 h-3 text-orange-500" />}
                      <span className="text-xs text-greta-500 capitalize">{transaction.status}</span>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        )}

        {/* Security Notice */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="bg-yellow-50 border border-yellow-200 rounded-2xl p-6"
        >
          <div className="flex items-center space-x-3 mb-3">
            <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
              <Settings className="w-4 h-4 text-yellow-600" />
            </div>
            <h4 className="font-semibold text-yellow-800">Security Features</h4>
          </div>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• All transactions require your app authentication</li>
            <li>• Bot uses secure tokenization for payments</li>
            <li>• No sensitive card data is stored in Telegram</li>
            <li>• Transaction limits apply for security</li>
          </ul>
        </motion.div>
      </div>

      <BottomNavigation />
    </div>
  );
};

export default TelegramIntegration;