import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import BottomNavigation from '../Navigation/BottomNavigation';
import { 
  ArrowLeft, 
  User, 
  Shield, 
  Bell, 
  CreditCard, 
  Smartphone, 
  HelpCircle, 
  LogOut, 
  ChevronRight, 
  Edit, 
  Lock, 
  Eye, 
  Settings as SettingsIcon 
} from '../Icons/Icons';

const Settings = () => {
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
  const navigate = useNavigate();
  const { user, logout } = useAuth();

  const settingsSections = [
    {
      title: 'Account',
      items: [
        {
          id: 'profile',
          icon: User,
          title: 'Profile Information',
          subtitle: 'Update your personal details',
          color: 'bg-blue-100 text-blue-600'
        },
        {
          id: 'security',
          icon: Shield,
          title: 'Security & Privacy',
          subtitle: 'Manage your security settings',
          color: 'bg-green-100 text-green-600'
        },
        {
          id: 'notifications',
          icon: Bell,
          title: 'Notifications',
          subtitle: 'Control your notification preferences',
          color: 'bg-yellow-100 text-yellow-600'
        }
      ]
    },
    {
      title: 'Payment',
      items: [
        {
          id: 'cards',
          icon: CreditCard,
          title: 'Payment Methods',
          subtitle: 'Manage your cards and accounts',
          color: 'bg-purple-100 text-purple-600'
        },
        {
          id: 'nfc-settings',
          icon: Smartphone,
          title: 'NFC Settings',
          subtitle: 'Configure contactless payments',
          color: 'bg-indigo-100 text-indigo-600'
        }
      ]
    },
    {
      title: 'Support',
      items: [
        {
          id: 'help',
          icon: HelpCircle,
          title: 'Help & Support',
          subtitle: 'Get help with your account',
          color: 'bg-orange-100 text-orange-600'
        }
      ]
    }
  ];

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  const handleSettingClick = (settingId) => {
    switch (settingId) {
      case 'cards':
        navigate('/cards');
        break;
      case 'nfc-settings':
        navigate('/nfc');
        break;
      default:
        // Handle other settings
        console.log(`Opening ${settingId} settings`);
    }
  };

  return (
    <div className="min-h-screen bg-greta-50 pb-20">
      {/* Header */}
      <motion.div
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="bg-white shadow-sm sticky top-0 z-10"
      >
        <div className="px-6 py-4 flex items-center space-x-4">
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => navigate('/dashboard')}
            className="p-2 hover:bg-greta-100 rounded-full transition-colors"
          >
            <ArrowLeft className="w-6 h-6 text-greta-700" />
          </motion.button>
          <div>
            <h1 className="text-xl font-bold text-greta-900">Settings</h1>
            <p className="text-sm text-greta-600">Manage your account and preferences</p>
          </div>
        </div>
      </motion.div>

      <div className="px-6 py-6">
        {/* Profile Card */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="bg-gradient-greta rounded-3xl p-6 text-white mb-6 shadow-card"
        >
          <div className="flex items-center space-x-4">
            <img
              src={user?.avatar}
              alt={user?.name}
              className="w-16 h-16 rounded-full border-3 border-white/30"
            />
            <div className="flex-1">
              <h2 className="text-xl font-bold">{user?.name}</h2>
              <p className="text-white/80">{user?.email}</p>
              <p className="text-white/70 text-sm">{user?.phone}</p>
            </div>
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              className="p-2 bg-white/20 rounded-full"
            >
              <Edit className="w-5 h-5" />
            </motion.button>
          </div>
        </motion.div>

        {/* Settings Sections */}
        <div className="space-y-6">
          {settingsSections.map((section, sectionIndex) => (
            <motion.div
              key={section.title}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: sectionIndex * 0.1 }}
            >
              <h3 className="text-sm font-semibold text-greta-700 mb-3 px-2">{section.title}</h3>
              <div className="bg-white rounded-2xl shadow-greta overflow-hidden">
                {section.items.map((item, itemIndex) => (
                  <motion.button
                    key={item.id}
                    whileHover={{ backgroundColor: '#f9fafb' }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => handleSettingClick(item.id)}
                    className="w-full p-4 border-b border-greta-100 last:border-b-0 flex items-center space-x-4 text-left transition-colors"
                  >
                    <div className={`w-10 h-10 ${item.color} rounded-full flex items-center justify-center`}>
                      <item.icon className="w-5 h-5" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-greta-900">{item.title}</h4>
                      <p className="text-sm text-greta-600">{item.subtitle}</p>
                    </div>
                    <ChevronRight className="w-5 h-5 text-greta-400" />
                  </motion.button>
                ))}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Quick Actions */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.4 }}
          className="mt-8"
        >
          <h3 className="text-sm font-semibold text-greta-700 mb-3 px-2">Quick Actions</h3>
          <div className="bg-white rounded-2xl shadow-greta overflow-hidden">
            <motion.button
              whileHover={{ backgroundColor: '#f9fafb' }}
              whileTap={{ scale: 0.98 }}
              className="w-full p-4 border-b border-greta-100 flex items-center space-x-4 text-left transition-colors"
            >
              <div className="w-10 h-10 bg-gray-100 text-gray-600 rounded-full flex items-center justify-center">
                <Lock className="w-5 h-5" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-greta-900">Change PIN</h4>
                <p className="text-sm text-greta-600">Update your security PIN</p>
              </div>
              <ChevronRight className="w-5 h-5 text-greta-400" />
            </motion.button>

            <motion.button
              whileHover={{ backgroundColor: '#f9fafb' }}
              whileTap={{ scale: 0.98 }}
              className="w-full p-4 flex items-center space-x-4 text-left transition-colors"
            >
              <div className="w-10 h-10 bg-gray-100 text-gray-600 rounded-full flex items-center justify-center">
                <Eye className="w-5 h-5" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-greta-900">Privacy Settings</h4>
                <p className="text-sm text-greta-600">Control your data and privacy</p>
              </div>
              <ChevronRight className="w-5 h-5 text-greta-400" />
            </motion.button>
          </div>
        </motion.div>

        {/* App Info */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="mt-8 text-center"
        >
          <div className="bg-white rounded-2xl p-6 shadow-greta">
            <div className="w-12 h-12 bg-gradient-greta rounded-full flex items-center justify-center mx-auto mb-4">
              <SettingsIcon className="w-6 h-6 text-white" />
            </div>
            <h3 className="font-semibold text-greta-900 mb-2">Greta Wallet</h3>
            <p className="text-sm text-greta-600 mb-4">Version 1.0.0</p>
            <p className="text-xs text-greta-500">
              Secure digital payments with NFC technology
            </p>
          </div>
        </motion.div>

        {/* Logout Button */}
        <motion.button
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.6 }}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => setShowLogoutConfirm(true)}
          className="w-full mt-8 bg-red-50 border border-red-200 text-red-600 py-4 rounded-2xl font-semibold flex items-center justify-center space-x-2 hover:bg-red-100 transition-all"
        >
          <LogOut className="w-5 h-5" />
          <span>Sign Out</span>
        </motion.button>
      </div>

      {/* Logout Confirmation Modal */}
      {showLogoutConfirm && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
          onClick={() => setShowLogoutConfirm(false)}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            onClick={(e) => e.stopPropagation()}
            className="bg-white rounded-3xl p-6 w-full max-w-sm shadow-float"
          >
            <div className="text-center">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <LogOut className="w-8 h-8 text-red-600" />
              </div>
              <h2 className="text-xl font-bold text-greta-900 mb-2">Sign Out</h2>
              <p className="text-greta-600 mb-6">
                Are you sure you want to sign out of your account?
              </p>
              <div className="flex space-x-3">
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => setShowLogoutConfirm(false)}
                  className="flex-1 bg-greta-200 text-greta-700 py-3 rounded-xl font-semibold"
                >
                  Cancel
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={handleLogout}
                  className="flex-1 bg-red-600 text-white py-3 rounded-xl font-semibold shadow-lg"
                >
                  Sign Out
                </motion.button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}

      <BottomNavigation />
    </div>
  );
};

export default Settings;